# Parenthing Server

Backend server implementation for Parenthing application using AWS Amplify.

## 🚀 Features

- **User Management**
  - Authentication & Authorization
  - Profile management
  - Device token management
  - Chat history & messaging
  - Notification preferences

- **Business Features**
  - Business registration & KYC
  - Class & event management 
  - Location management
  - Profile & notification management

- **Admin Features**
  - User/Business approval workflow
  - Report management
  - Analytics dashboard
  - Export functionality

- **Notification System**
  - Push notifications
  - WhatsApp integration
  - Email notifications
  - SMS notifications via MSG91

## 🛠️ Tech Stack

- Node.js
- Express.js
- AWS Amplify
- MySQL
- Firebase Admin (Push Notifications)
- MSG91 (SMS & WhatsApp)

## 📋 Setup Instructions

1. Install dependencies:
```bash
npm install
```

2. Initialize Amplify:
```bash
amplify init
```

3. Push the backend:
```bash
amplify push
```

## 🔄 Environment Management

### Staging to Production Deployment

1. Check Current Status:
```bash
amplify status
amplify env list
```

2. Switch to Production:
```bash
amplify env checkout production
```

3. Push Changes:
```bash
amplify push
```

4. Return to Staging:
```bash
amplify env checkout staging
```

## 📦 API Functions

- `appconfig` - Application configuration management
- `business` - Business-related operations
- `classes` - Class management
- `events` - Event management
- `media` - Media upload and management
- `notifications` - Notification system
- `onboarding` - User onboarding process
- `superadmin` - Admin operations
- `upload` - File upload handling
- `user` - User management

## 🔒 Environment Variables

Required environment variables:
- `MSG91_AUTH_KEY` - MSG91 authentication key
- `AWS_REGION` - AWS region
- `ENV` - Environment (staging/production)

## 📚 Documentation

For detailed API documentation and integration guides, please refer to:
- [AWS Amplify Docs](https://docs.amplify.aws)
- [API Documentation](./docs/api.md)


