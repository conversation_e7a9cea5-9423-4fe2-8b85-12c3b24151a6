import {
  getFirebaseNotification,
  getEventsHappeningToday,
  getEventsHappeningTomorrow,
  updateNotificationTable,
  profileCompleteReminderService,
  getFreeEventNearbyLocation,
  findAndNotifyMatchingClassesService,
  getNearbyParentLocation,
  getPendingMatches,
} from "../service/NotificationsService.js";
import db from "../config/db.js";

export default class NotificationController {

  // static async getProfileUpdateReminderNotification() {
  //   console.log("Job is running");
  //   try {
  //     const sql = `CALL spGetOutdatedUsersAndBusinesses()`;
  //     const [result] = await db.query(sql);
  //     const users = result[0];
  //     console.log("users", users);

  //     users.forEach((user) => {
  //       console.log(user);
  //       if (!user.token) {
  //         console.log(`${user.business_name} does not have token`);
  //         return;
  //       }

  //       const message = user?.city
  //         ? `${user?.business_name}, do you still operate in ${user?.city}? Review your profile to ensure everything is up to date.`
  //         : `${user?.business_name}, is your contact number still ${user?.contact_number}? Review your profile to ensure everything is up to date.`;

  //       sendReminderNotification(user, message);
  //     });
  //   } catch (error) {
  //     console.error("Error executing scheduled job", error);
  //   }
  // }

  //App notification:

  static async sendEventRemindersNotification(req, res) {
    try {
      console.log("🔵 Starting event reminders notification process...");
      
      // Fetch events happening today
      const todayEvents = await getEventsHappeningToday();
      console.log("Today's Events:", todayEvents);

      // Fetch events happening tomorrow
      const tomorrowEvents = await getEventsHappeningTomorrow();
      console.log("Tomorrow's Events:", tomorrowEvents);

      // Combine both today and tomorrow events
      const allEvents = [...todayEvents, ...tomorrowEvents];
      
      let successCount = 0;
      let failureCount = 0;
      let notificationsSent = 0;

      // Loop through the combined events and send notifications
      for (const event of allEvents) {
        const { user_id, title, bookmarked_id } = event;
        let messageTitle, msg;

        if (todayEvents.includes(event)) {
          messageTitle = "Bookmarked event is today";
          msg = `Your saved event, ${title}, is happening today. Don't miss it!`;
        } else {
          messageTitle = "Bookmarked event is near";
          msg = `Your saved event, ${title}, is happening tomorrow. Have you booked your seat?`;
        }

        const type = "event";

        // Prepare metadata with event IDs array
        const metadata = {
          event_ids: [bookmarked_id]
        };

        try {
          const notification = await getFirebaseNotification(
            user_id,
            messageTitle,
            msg,
            type
          );
          
          console.log("Notification sent:", notification);

          if (notification) {
            await updateNotificationTable(
              user_id,
              null,
              1,
              messageTitle,
              msg,
              type,
              JSON.stringify(metadata) // Convert metadata to JSON string
            );
            notificationsSent++;
            successCount++;
            console.log(`✅ Notification sent for event ${bookmarked_id}`);
          }
        } catch (error) {
          console.error(`❌ Error processing event ${bookmarked_id}:`, error);
          failureCount++;
        }
      }

      // Final summary
      const summary = {
        totalEvents: allEvents.length,
        successfulNotifications: successCount,
        failedNotifications: failureCount,
        notificationsSent,
        completionTime: new Date().toISOString()
      };

      console.log("\n🏁 Final Summary:", summary);

      // Handle both API and direct calls
      if (req && res) {
        return res.status(200).json({
          message: "Event reminders sent successfully",
          summary
        });
      }
      
      return summary;

    } catch (error) {
      console.error("❌ Error sending event reminders:", error);
      if (req && res) {
        return res.status(500).json({ 
          error: "An error occurred while sending event reminders.",
          details: error.message 
        });
      }
      throw error;
    }
  }

  static async sendProfileCompletionRemindersNotification(req, res) {
  try {
    console.log("🔵 Starting profile completion reminder process...");
    
    // Get all active parents with their names
    const sql = `
      SELECT p.parent_id, p.user_id, p.first_name 
      FROM Parents p 
      WHERE p.is_deleted = 0 AND p.is_disabled = 0
    `;
    const [allParents] = await db.query(sql);
    
    const BATCH_SIZE = 500;
    const totalParents = allParents.length;
    const totalBatches = Math.ceil(totalParents / BATCH_SIZE);
    
    console.log(`📊 Total parents: ${totalParents}`);
    console.log(`📦 Total batches: ${totalBatches} (${BATCH_SIZE} parents per batch)`);

    let successCount = 0;
    let failureCount = 0;
    let notificationsSent = 0;

    // Process in batches
    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const start = batchIndex * BATCH_SIZE;
      const end = Math.min(start + BATCH_SIZE, totalParents);
      const currentBatch = allParents.slice(start, end);
      
      console.log(`\n🔄 Processing batch ${batchIndex + 1}/${totalBatches}`);
      console.log(`⏳ Processing parents ${start + 1} to ${end}`);

      // Process each parent in batch
      for (const parent of currentBatch) {
        try {
          // Check profile completion status
          const profileStatus = await profileCompleteReminderService(parent.parent_id);
          
          if (profileStatus?.status === 0) {
            // Prepare notification content
            const messageTitle = "Complete Your Profile!";
            const msg = `${parent.first_name || 'Hi'}, complete your profile today to get even better recommendations.`;
            const type = "user";

            try {
              const notification = await getFirebaseNotification(
                parent.user_id,
                messageTitle,
                msg,
                type
              );

              if (notification) {
                await updateNotificationTable(
                  parent.user_id,
                  null,
                  1,
                  messageTitle,
                  msg,
                  type
                );
                
                notificationsSent++;
                successCount++;
                console.log(`✅ Reminder sent to parent ${parent.parent_id}`);
              }
            } catch (error) {
              console.error(`❌ Error sending reminder to parent ${parent.parent_id}:`, error);
              failureCount++;
            }
          }
        } catch (error) {
          console.error(`❌ Error processing parent ${parent.parent_id}:`, error);
          failureCount++;
        }
      }

      // Batch summary
      console.log(`\n📈 Batch ${batchIndex + 1} Summary:`);
      console.log(`- Processed: ${currentBatch.length} parents`);
      console.log(`- Success: ${successCount}`);
      console.log(`- Failures: ${failureCount}`);
    }

    // Final summary
    const summary = {
      totalParents,
      totalBatches,
      successfulNotifications: successCount,
      failedNotifications: failureCount,
      notificationsSent,
      completionTime: new Date().toISOString()
    };

    console.log("\n🏁 Final Summary:", summary);

    // Handle both API and direct calls
    if (req && res) {
      return res.status(200).json({
        message: "Profile completion reminders process completed",
        summary
      });
    }
    
    return summary;

  } catch (error) {
    console.error("❌ Error in profile completion process:", error);
    if (req && res) {
      return res.status(500).json({
        message: "Error processing profile completion reminders",
        error: error.message
      });
    }
    throw error;
  }
  }

  static async sendFreeEventNearbyUserLocationNotificaion(req, res) {
  try {
    // Step 1: Fetch all user IDs from the Users table
    const [rows] = await db.query("SELECT user_id FROM Users");

    // Step 2: Loop through each user ID
    for (const row of rows) {
      const userId = row.user_id;
      console.log("Processing user:", userId);
      
      const [[parent]] = await db.query(
        "SELECT latitude, longitude FROM Parents WHERE user_id = ?",
        [userId]
      );
      
      if (!parent || !parent.latitude || !parent.longitude) {
        console.log(`Skipping user ${userId} - no location data`);
        continue;
      }

      console.log("Parent location:", parent.latitude, parent.longitude);

      // Get actual events data instead of just count
      const events = await getFreeEventNearbyLocation(
        parent.latitude,
        parent.longitude
      );

      console.log("Free events found:", events.length);

      // Only send notification if there are free events
      if (events.length > 0) {
        // Filter only truly free events (price = 0 or null)
        const actualFreeEvents = events.filter(event => 
          event.ticket_type === 'free' && (event.price === 0 || event.price === null)
        );

        console.log("Actual free events after filtering:", actualFreeEvents.length);

        if (actualFreeEvents.length > 0) {
          const messageTitle = "Free Events Nearby";
          const messageBody = `There are ${actualFreeEvents.length} free event${actualFreeEvents.length > 1 ? 's' : ''} nearby. Book before seats fill up!`;
          const type = "event";

          // Prepare metadata with only event IDs
          const metadata = {
            event_ids: actualFreeEvents.map(event => event.event_id)
          };

          // Step 3: Send notification to user
          const notification = await getFirebaseNotification(
            userId,
            messageTitle,
            messageBody,
            type
          );

          if (notification) {
            try {
              await updateNotificationTable(
                userId,
                null,
                1,
                messageTitle,
                messageBody,
                type,
                JSON.stringify(metadata) // Pass metadata as JSON string
              );
              console.log(`Notification sent to user ${userId} for ${actualFreeEvents.length} free events`);
            } catch (error) {
              console.error("Error inserting notification record:", error);
            }
          }
        } else {
          console.log(`No actual free events found for user ${userId} after filtering`);
        }
      } else {
        console.log(`No events found for user ${userId}`);
      }
    }

    console.log("Notifications processing completed for all users.");
    return res.status(200).send({
      message: "Notifications sent to eligible users successfully.",
    });
  } catch (error) {
    console.error("Error sending notifications:", error);
    return res.status(500).send({
      message: "Error sending notifications.",
      error: error.message,
    });
  }
  }

  static async notifySimilarClasses(req, res) {
    try {
      console.log("🔵 Starting similar classes notification process...");
      
      // Get all users who have bookmarked classes
      const [users] = await db.query("SELECT DISTINCT parent_id FROM classbookmarks");
      
      const BATCH_SIZE = 500;
      const totalUsers = users?.length || 0;
      const totalBatches = Math.ceil(totalUsers / BATCH_SIZE);
      
      console.log(`📊 Total users with bookmarked classes: ${totalUsers}`);
      console.log(`📦 Total batches: ${totalBatches} (${BATCH_SIZE} users per batch)`);

      let successCount = 0;
      let failureCount = 0;
      let notificationsSent = 0;
      let skippedCount = 0;

      // Handle no users case
      if (!users || users.length === 0) {
        console.log("⚠️ No users found with bookmarked classes");
        const summary = { 
          totalUsers: 0, 
          skippedNotifications: 0, 
          successfulNotifications: 0, 
          failedNotifications: 0,
          notificationsSent: 0,
          completionTime: new Date().toISOString()
        };
        
        if (req && res) {
          return res.status(404).json({ 
            message: "No users found in classbookmarks", 
            summary 
          });
        }
        return summary;
      }

      // Process in batches
      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const start = batchIndex * BATCH_SIZE;
        const end = Math.min(start + BATCH_SIZE, totalUsers);
        const currentBatch = users.slice(start, end);
        
        console.log(`\n🔄 Processing batch ${batchIndex + 1}/${totalBatches}`);
        console.log(`⏳ Processing users ${start + 1} to ${end}`);

        // Process each user in batch
        for (const user of currentBatch) {
          try {
            const fetchedParentId = user.parent_id;
            console.log(`\n👤 Processing parent ID: ${fetchedParentId}`);

            // Get matching classes from service
            const matchingClasses = await findAndNotifyMatchingClassesService(null, fetchedParentId);
            
            if (!matchingClasses) {
              console.log(`ℹ️ No matching classes for parent ${fetchedParentId}`);
              skippedCount++;
              continue;
            }

            // Get user details for notification
            const [[parentDetails]] = await db.query(
              `SELECT user_id, parent_name FROM Parents WHERE parent_id = ?`,
              [fetchedParentId]
            );

            if (!parentDetails?.user_id) {
              console.log(`⚠️ No user found for parent ${fetchedParentId}`);
              skippedCount++;
              continue;
            }

            // Prepare notification content
            const messageTitle = `We found more similar classes for you`;
            const msg = `Looking for classes? We've got you covered`;
            const type = "class";

            // Prepare metadata with class IDs
            const metadata = {
              class_ids: matchingClasses.map(c => c.class_id)
            };

            try {
              const notification = await getFirebaseNotification(
                parentDetails.user_id,
                messageTitle,
                msg,
                type
              );

              if (notification) {
                await updateNotificationTable(
                  parentDetails.user_id,
                  null,
                  1,
                  messageTitle,
                  msg,
                  type,
                  JSON.stringify(metadata)
                );
                
                notificationsSent++;
                successCount++;
                console.log(`✅ Notification sent to parent ${fetchedParentId} for ${matchingClasses.length} classes`);
              }
            } catch (error) {
              console.error(`❌ Error sending notification to parent ${fetchedParentId}:`, error);
              failureCount++;
            }
          } catch (error) {
            console.error(`❌ Error processing parent ${user.parent_id}:`, error);
            failureCount++;
          }
        }

        // Batch summary
        console.log(`\n📈 Batch ${batchIndex + 1} Summary:`);
        console.log(`- Processed: ${currentBatch.length} users`);
        console.log(`- Success: ${successCount}`);
        console.log(`- Skipped: ${skippedCount}`);
        console.log(`- Failures: ${failureCount}`);
      }

      // Final summary
      const summary = {
        totalUsers,
        totalBatches,
        successfulNotifications: successCount,
        failedNotifications: failureCount,
        skippedNotifications: skippedCount,
        notificationsSent,
        completionTime: new Date().toISOString()
      };

      console.log("\n🏁 Final Summary:", summary);

      // Handle both API and direct calls
      if (req && res) {
        return res.status(200).json({
          message: "Similar classes notifications completed",
          summary
        });
      }
      
      return summary;

    } catch (error) {
      console.error("❌ Error in similar classes process:", error);
      if (req && res) {
        return res.status(500).json({
          message: "Error processing similar classes notifications",
          error: error.message
        });
      }
      throw error;
    }
  }

  static async nearByParents(req, res) {
  try {
    console.log("🔵 Starting nearby parents notification process...");
    
    // Get all active parents
    const sql = "SELECT parent_id, user_id, latitude, longitude FROM Parents WHERE is_deleted = 0 AND is_disabled = 0";
    const [allParents] = await db.query(sql);
    
    const BATCH_SIZE = 500;
    const totalParents = allParents.length;
    const totalBatches = Math.ceil(totalParents / BATCH_SIZE);
    
    console.log(`📊 Total parents: ${totalParents}`);
    console.log(`📦 Total batches: ${totalBatches} (${BATCH_SIZE} parents per batch)`);

    let successCount = 0;
    let failureCount = 0;
    let notificationsSent = 0;

    // Process in batches
    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const start = batchIndex * BATCH_SIZE;
      const end = Math.min(start + BATCH_SIZE, totalParents);
      const currentBatch = allParents.slice(start, end);
      
      console.log(`\n🔄 Processing batch ${batchIndex + 1}/${totalBatches}`);
      console.log(`⏳ Processing parents ${start + 1} to ${end}`);

      // Process each parent in the batch
      for (const parent of currentBatch) {
        try {
          if (!parent.latitude || !parent.longitude) {
            console.log(`⚠️ Skipping parent ${parent.parent_id} - missing location data`);
            continue;
          }

          // Get nearby parents for current parent
          const nearbyParentsResult = await getNearbyParentLocation(parent.parent_id);
          
          if (nearbyParentsResult.count > 0) {
            // Prepare notification content
            const title = "New Matches Found!";
            const msg = `${nearbyParentsResult.count} new matches found near you! Connect to start chatting.`;
            const type = "user";

            // Send notification with correct parameters
            try {
              const notification = await getFirebaseNotification(
                parent.user_id,
                title,
                msg,
                type
              );

              if (notification) {
                // Update notification table with metadata
                await updateNotificationTable(
                  parent.user_id,
                  null,
                  1,
                  title,
                  msg,
                  type,
                  JSON.stringify({ nearby_parent_ids: nearbyParentsResult.parents.map(p => p.parent_id) })
                );
                
                notificationsSent++;
                successCount++;
                console.log(`✅ Notification sent to parent ${parent.parent_id} for ${nearbyParentsResult.count} matches`);
              }
            } catch (error) {
              console.error(`❌ Error sending notification to parent ${parent.parent_id}:`, error);
              failureCount++;
            }
          }
        } catch (error) {
          console.error(`❌ Error processing parent ${parent.parent_id}:`, error);
          failureCount++;
        }
      }

      // Batch summary
      console.log(`\n📈 Batch ${batchIndex + 1} Summary:`);
      console.log(`- Processed: ${currentBatch.length} parents`);
      console.log(`- Success: ${successCount}`);
      console.log(`- Failures: ${failureCount}`);
    }

    // Final summary
    const summary = {
      totalParents,
      totalBatches,
      successfulNotifications: successCount,
      failedNotifications: failureCount,
      notificationsSent,
      completionTime: new Date().toISOString()
    };

    console.log("\n🏁 Final Summary:", summary);

    // Only send response if req and res are defined (API context)
    if (req && res) {
      return res.status(200).json({
        message: "Nearby parents notifications process completed",
        summary
      });
    }
    
    return summary;

  } catch (error) {
    console.error("❌ Error in nearby parents process:", error);
    if (req && res) {
      return res.status(500).json({
        message: "Error processing nearby parents notifications",
        error: error.message
      });
    }
    throw error;
  }
  }

  static async sendPendingMatchesNotifications(req, res) {
    try {
      console.log("🔵 Starting pending matches notification process...");
      
      // Get all users with pending matches
      const pendingMatches = await getPendingMatches();
      
      const BATCH_SIZE = 500;
      const totalUsers = pendingMatches.length;
      const totalBatches = Math.ceil(totalUsers / BATCH_SIZE);
      
      console.log(`📊 Total users with pending matches: ${totalUsers}`);
      console.log(`📦 Total batches: ${totalBatches} (${BATCH_SIZE} users per batch)`);

      let successCount = 0;
      let failureCount = 0;
      let notificationsSent = 0;

      // Process in batches
      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const start = batchIndex * BATCH_SIZE;
        const end = Math.min(start + BATCH_SIZE, totalUsers);
        const currentBatch = pendingMatches.slice(start, end);
        
        console.log(`\n🔄 Processing batch ${batchIndex + 1}/${totalBatches}`);
        console.log(`⏳ Processing users ${start + 1} to ${end}`);

        // Process each user in the batch
        for (const user of currentBatch) {
          try {
            // Prepare notification content
            const title = "Pending Matches";
            const msg = `${user.first_name || 'Hi'}, you have ${user.pending_matches} new connection ${user.pending_matches > 1 ? 'requests' : 'request'}!`;
            const type = "Chat";

            // Prepare metadata with connection IDs
            const metadata = {
              connection_ids: user.connection_ids.split(',').map(Number)
            };

            try {
              const notification = await getFirebaseNotification(
                user.user_id,
                title,
                msg,
                type
              );

              if (notification) {
                await updateNotificationTable(
                  user.user_id,
                  null,
                  1,
                  title,
                  msg,
                  type,
                  JSON.stringify(metadata)
                );
                
                notificationsSent++;
                successCount++;
                console.log(`✅ Notification sent to user ${user.user_id} for ${user.pending_matches} matches`);
              }
            } catch (error) {
              console.error(`❌ Error sending notification to user ${user.user_id}:`, error);
              failureCount++;
            }
          } catch (error) {
            console.error(`❌ Error processing user ${user.user_id}:`, error);
            failureCount++;
          }
        }

        // Batch summary
        console.log(`\n📈 Batch ${batchIndex + 1} Summary:`);
        console.log(`- Processed: ${currentBatch.length} users`);
        console.log(`- Success: ${successCount}`);
        console.log(`- Failures: ${failureCount}`);
      }

      // Final summary
      const summary = {
        totalUsers,
        totalBatches,
        successfulNotifications: successCount,
        failedNotifications: failureCount,
        notificationsSent,
        completionTime: new Date().toISOString()
      };

      console.log("\n🏁 Final Summary:", summary);

      // Handle both API and direct calls
      if (req && res) {
        return res.status(200).json({
          message: "Pending matches notifications completed",
          summary
        });
      }
      
      return summary;

    } catch (error) {
      console.error("❌ Error in pending matches process:", error);
      if (req && res) {
        return res.status(500).json({
          message: "Error processing pending matches notifications",
          error: error.message
        });
      }
      throw error;
    }
  }

}
