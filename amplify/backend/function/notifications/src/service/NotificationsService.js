import admin from "firebase-admin";

// Initialize Firebase Admin SDK
import serviceAccount from "../config/firebase.json" assert { type: "json" }; // Update with the correct path to your service account key file
//import db from "../db.js";
import db from "../config/db.js";
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});


// Universal firebase notification sending function & database saving:

export const updateNotificationTable = async (
  user_id,
  connection_id,
  role_id,
  title,
  msg,
  notification_type,
  metadata = null
) => {
  try {
    const [result] = await db.query(
      "CALL spInsertNotification(?, ?, ?, ?, ?, ?, ?)",
      [user_id, connection_id, role_id, title, msg, notification_type, metadata]
    );
    return result;
  } catch (error) {
    console.error("Error inserting notification:", error);
    throw error;
  }
};

export const getFirebaseNotification = async (user_id, title, message, type) => {
  try {
    const sql = `SELECT firebase_id AS fire_base_id FROM Users WHERE user_id = ?`;
    const [[user]] = await db.query(sql, [user_id]);

    // Early return if no firebase token found
    if (!user?.fire_base_id || user.fire_base_id.trim() === '') {
      console.log("⚠️ Firebase token not found or empty for user:", user_id);
      return null;
    }

    const data = {
      title: String(title),
      body: String(message),
      notification_type: type
    };

    const messagePayload = {
      data: data,
      apns: {
        headers: {
          "apns-priority": "10",
        },
        payload: {
          aps: {
            badge: 0,
            sound: "default",
            alert: {
              title: data.title,
              body: data.body,
              type: data.notification_type,
            },
          },
        },
        fcm_options: {
          image: "https://parenthing.s3.ap-south-1.amazonaws.com/dummy/banner.png",
        },
      },
      token: user.fire_base_id
    };

    const msg = await admin.messaging().send(messagePayload);
    // console.log(`✅ Notification sent to user ${user_id}`);
    return msg;
  } catch (error) {
    if (error.errorInfo?.code === 'messaging/registration-token-not-registered') {
      console.log(`⚠️ Invalid Firebase token for user ${user_id}`);
      return null;
    }
    console.error(`❌ Error sending notification to user ${user_id}:`, error);
    throw error;
  }
};


//App service:

export const getEventsHappeningToday = async () => {
  const [[rows]] = await db.query("CALL spGetEventsHappeningToday()");
  return rows;
};

export const getEventsHappeningTomorrow = async () => {
  const [[rows]] = await db.query("CALL spGetEventsHappeningTomorrow()");
  return rows;
};

// for profile incomplete
export const profileCompleteReminderService = async (parentId) => {
  try {
    const [result] = await db.query(
      `CALL spIsProfileComplete(JSON_OBJECT('parent_id', ?))`,
      [parentId]
    );
    return result[0][0]; // Returns { user_id, first_name, status }
  } catch (error) {
    console.error(`❌ Error checking profile completion for parent ${parentId}:`, error);
    throw error;
  }
};

// for free notification in user locality
export const getFreeEventNearbyLocation = async (latitude, longitude) => {
  try {
    // Call the stored procedure with the given parameters
    const [rows] = await db.query(
      "CALL parenthingcomplete_uat.spGetNearbyFreeEvents(?, ?, ?)",
      [latitude, longitude, 10.0]
    );
    // Return the actual events data instead of just count
    console.log("Free events found:", rows[0]?.length || 0);
    return rows[0] || []; // Return first result set from stored procedure
  } catch (error) {
    console.error("Error fetching nearby free events:", error);
    throw error;
  }
};

export const findAndNotifyMatchingClassesService = async (
  className,
  parentId
) => {
  try {
    console.log(`Calling stored procedure with parentId: ${parentId}`);

    // Call the stored procedure
    const [[rows]] = await db.query(
      "CALL parenthingcomplete_uat.spFindSimilarClasses(?)",
      [parentId]
    );

    // console.log(`Stored procedure returned rows: ${JSON.stringify(rows)}`);
    // Check if rows are returned
    if (!rows || rows.length === 0 || !rows[0] || rows[0].length === 0) {
      console.log("No matching classes found.");
      return;
    }
    // Loop through the results and prepare notifications
    // for (const row of rows[0]) {
    //   const matchingClassName = row.matching_class_name;

    //   // Prepare notification details
    //   const title = `We found more ${matchingClassName} matching classes for you`;
    //   const body = `Looking for ${matchingClassName}? We've got you!`;

    //   // Send notification using your existing method
    //   await getFirebaseNotification(parentId, title, body);
    // }
    return rows;
  } catch (error) {
    console.error("Error finding and notifying similar classes:", error);
    throw error;
  }
};

// near by parents
export const getNearbyParentLocation = async (currentParentId) => {
  try {
    // Call the stored procedure for a single parent
    const [results] = await db.query(
      "CALL spGetNearbyParents(?, ?)",
      [currentParentId, 10.0] // 10km radius
    );

    // First result set contains the nearby parents
    const nearbyParents = results[0] || [];

    return {
      count: nearbyParents.length,  // Single count of actual matches
      parents: nearbyParents        // Array of parent details
    };
  } catch (error) {
    console.error(`Error fetching nearby parents for parent ${currentParentId}:`, error);
    throw error;
  }
};

export const getPendingMatches = async () => {
  try {
    // Query to get users with pending connection requests
    const [rows] = await db.query(`
      SELECT 
        cm.receiver_id,
        u.user_id,
        p.first_name,
        GROUP_CONCAT(cm.connection_id) as connection_ids,
        COUNT(cm.connection_id) AS pending_matches
      FROM 
        parenthingcomplete_uat.Users u
      JOIN 
        parenthingcomplete_uat.Connection_Master cm ON u.parent_id = cm.receiver_id
      JOIN
        parenthingcomplete_uat.Parents p ON u.parent_id = p.parent_id
      WHERE 
        cm.status = 'send' 
        AND cm.is_accept = 0
      GROUP BY 
        cm.receiver_id, u.user_id, p.first_name
    `);

    console.log(`📊 Found ${rows.length} users with pending matches`);
    return rows;
  } catch (error) {
    console.error("❌ Error fetching pending matches:", error);
    throw error;
  }
};
