/*
Copyright 2017 - 2017 Amazon.com, Inc. or its affiliates. All Rights Reserved.
Licensed under the Apache License, Version 2.0 (the "License"). You may not use this file except in compliance with the License. A copy of the License is located at
    http://aws.amazon.com/apache2.0/
or in the "license" file accompanying this file. This file is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and limitations under the License.
*/

const express = require("express");
const bodyParser = require("body-parser");
const awsServerlessExpressMiddleware = require("aws-serverless-express/middleware");
const BusinessController = require("./controller/BusinessController");
const Authorization = require("./auth/tokenValidator");

// declare a new express app
const app = express();
app.use(bodyParser.json());
app.use(awsServerlessExpressMiddleware.eventContext());

// Enable CORS for all methods
app.use(function (req, res, next) {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Headers", "*");
  res.header("Access-Control-Allow-Credentials", "true");
  next();
});

/**********************
 * Example get method *
 **********************/

app.get(
  "/dashboard/business/businessList",
  Authorization,
  BusinessController.getBusinessList
);
app.get(
  "/dashboard/business/getCategory",
  Authorization,
  BusinessController.categoryList
);

app.get("/dashboard/business", function (req, res) {
  // Add your code here
  res.json({ success: "get call succeed!", url: req.url });
});

app.get("/dashboard/business/*", function (req, res) {
  // Add your code here
  res.json({ success: "get call succeed!", url: req.url });
});

/****************************
 * Example post method *
 ****************************/
app.post(
  "/dashboard/business/get_places",
  BusinessController.getLocationByLatLong
);
app.post(
  "/dashboard/business/get_address",
  BusinessController.getLocationByAddress
);

app.post("/dashboard/business/generateotp", BusinessController.sendOtp);
app.post("/dashboard/business/verifyOtp", BusinessController.verifyOtp);
app.post("/dashboard/business/editProfile", BusinessController.editProfile);
app.post("/dashboard/business/logOutWeb", BusinessController.logOutWeb);

app.post(
  "/dashboard/business/saveDeviceTokenWeb",
  BusinessController.saveDeviceTokenWeb
);

app.post(
  "/dashboard/business/getNotificationList",
  BusinessController.getNotificationList
);

app.post(
  "/dashboard/business/updateNotification",
  BusinessController.updateBusinessNotification
);

app.post(
  "/dashboard/business/getBusinessClassEventsTotal",
  BusinessController.totalClassesEvents
);

app.post(
  "/dashboard/business/homepageImages",
  BusinessController.getHomepageImages
);

app.post(
  "/dashboard/business/registerBusiness",
  BusinessController.registerBusiness
);
app.post("/dashboard/business/addLocation", BusinessController.addLocation);

app.post("/dashboard/business/editLocation", BusinessController.editLocation);

app.post(
  "/dashboard/business/deleteLocationbyId",
  BusinessController.deleteLocationbyId
);

app.post(
  "/dashboard/business/editEventById",
  Authorization,
  BusinessController.editEventById
);
app.post(
  "/dashboard/business/editClassById",
  Authorization,
  BusinessController.editClassById
);
app.post(
  "/dashboard/business/event/getListByBusinessId",
  Authorization,
  BusinessController.getEventByBusinessId
);
app.post(
  "/dashboard/business/classList",
  Authorization,
  BusinessController.classList
);
app.post(
  "/dashboard/business/class/getClassById",
  Authorization,
  BusinessController.getClassById
);
app.post(
  "/dashboard/business/class/deleteClassById",
  Authorization,
  BusinessController.deleteClassById
);
app.post(
  "/dashboard/business/event/deleteEventById",
  Authorization,
  BusinessController.deleteEventById
);
app.post(
  "/dashboard/business/event/getEventById",
  Authorization,
  BusinessController.getEventById
);
app.post(
  "/dashboard/business/createClass",
  Authorization,
  BusinessController.createClass
);
app.post(
  "/dashboard/business/createEvent",
  Authorization,
  BusinessController.createEvent
);
app.post(
  "/dashboard/business/getUserById/:business_id",
  Authorization,
  BusinessController.getUserById
);
app.post("/dashboard/business/kyc", Authorization, BusinessController.kyc);

app.post("/dashboard/business/getKycDetails", BusinessController.getKycDetails);

app.post(
  "/dashboard/business/updateLastActive",
  BusinessController.updateLastActive
);

app.post("/dashboard/business", function (req, res) {
  // Add your code here
  res.json({ success: "post call succeed!", url: req.url, body: req.body });
});

app.post("/dashboard/business/*", function (req, res) {
  // Add your code here
  res.json({ success: "post call succeed!", url: req.url, body: req.body });
});


/****************************
 * Example put method *
 ****************************/

app.put("/dashboard/business", function (req, res) {
  // Add your code here
  res.json({ success: "put call succeed!", url: req.url, body: req.body });
});

app.put("/dashboard/business/*", function (req, res) {
  // Add your code here
  res.json({ success: "put call succeed!", url: req.url, body: req.body });
});

/****************************
 * Example delete method *
 ****************************/

app.delete("/dashboard/business", function (req, res) {
  // Add your code here
  res.json({ success: "delete call succeed!", url: req.url });
});

app.delete("/dashboard/business/*", function (req, res) {
  // Add your code here
  res.json({ success: "delete call succeed!", url: req.url });
});

app.listen(3000, function () {
  console.log("App started");
});

// Export the app object. When executing the application local this does nothing. However,
// to port it to AWS Lambda we will create a wrapper around that will load the app from
// this file
module.exports = app;
