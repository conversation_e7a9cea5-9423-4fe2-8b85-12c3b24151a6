const db = require("../config/db");
const axios = require("axios");
const MSG91_AUTH_KEY = "420745AQofv3Fn66332257P1";

const msg91OtpService = async (mobile, otp, templateId) => {
  try {
    // Only work in production environment
    if (process.env.ENV !== 'production') {
      console.log(`OTP disabled in ${process.env.ENV} environment`);
      return { status: true, msg: 'OTP disabled in staging', data: {} };
    }
    
    const config = {
      method: "post",
      url: `https://control.msg91.com/api/v5/otp?template_id=${templateId}&mobile=91${mobile}&authkey=${MSG91_AUTH_KEY}&otp=${otp}`,
      headers: {
        "Content-Type": "application/json",
      },
    };

    const response = await axios(config);
    const responseData = response.data;
    console.log("MSG91 OTP Response:", responseData);

    if (responseData.type === "success") {
      return { status: true, msg: responseData.type, data: {} };
    } else {
      return { status: false, msg: "Failed to send OTP", data: responseData };
    }
  } catch (error) {
    console.log("MSG91 OTP Error:", error);
    return { status: false, msg: "Error sending OTP", data: {} };
  }
};

const msg91WhatsappService = async (mobile, templateName, bodyValues = {}) => {
  try {
    // Only work in production environment
    if (process.env.ENV !== 'production') {
      console.log(`WhatsApp disabled in ${process.env.ENV} environment`);
      return { status: true, msg: 'WhatsApp disabled in staging', data: {} };
    }

    // Prepare components based on bodyValues
    const components = {};
    Object.keys(bodyValues).forEach((key, index) => {
      components[`body_${index + 1}`] = {
        type: "text",
        value: bodyValues[key]
      };
    });

    const whatsappData = {
      integrated_number: "918530572636",
      content_type: "template",
      payload: {
        messaging_product: "whatsapp",
        type: "template",
        template: {
          name: templateName,
          language: {
            code: "en",
            policy: "deterministic"
          },
          namespace: "78dd51ad_cb40_4786_b158_e43e2fc14a36",
          to_and_components: [
            {
              to: [`91${mobile}`],
              components: components
            }
          ]
        }
      }
    };

    const options = {
      method: 'POST',
      url: 'https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/',
      headers: {
        'Content-Type': 'application/json',
        'authkey': MSG91_AUTH_KEY
      },
      data: whatsappData
    };

    console.log('WhatsApp Data:', JSON.stringify(whatsappData, null, 2));
    
    const response = await axios(options);
    console.log('WhatsApp Response:', response.data);
    
    return { 
      status: true, 
      msg: 'WhatsApp sent successfully', 
      data: response.data 
    };
  } catch (error) {
    console.error('WhatsApp Error:', error.response?.data || error.message);
    return { 
      status: false, 
      msg: 'Error sending WhatsApp', 
      data: error.response?.data || {} 
    };
  }
};

const sendOtpService = async (payload) => {
  try {
    console.log("payload in otp:>>", payload);
    const procedure = `CALL spSendOtpV1(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const verifyOtpService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const userPayload = { ...payload, role_id: 3 };
    const procedure = `CALL spverifyOtp3(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(userPayload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const saveDeviceTokenWebService = async (payload) => {
  try {
    const procedure = `CALL spSaveDeviceTokenWeb(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("row", row[0]);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error", error);
  }
};

const logOutWebService = async (payload) => {
  try {
    console.log("payload in otp:>>", payload);
    const procedure = `CALL spLogOutWeb(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    // console.log("row::>>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const getHomepageImagesService = async (payload) => {
  try {
    const procedure = `CALL spHomepageImages(?)`;
    const [[rows]] = await db
      .promise()
      .query(procedure, [JSON.stringify(payload)]);

    console.log("Fetched images:", rows);

    if (rows) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error while fetching images", error);
  }
};

const registerBusinessService = async (payload) => {
  try {
    const procedure = `CALL spRegisterBusiness(?)`;
    const [rows] = await db
      .promise()
      .query(procedure, [JSON.stringify(payload)]);

    console.log("Register business:", rows);

    if (rows) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error while creating business", error);
    throw error; // Optionally handle or rethrow the error for further handling
  }
};

const addLocationService = async (payload) => {
  try {
    const procedure = `CALL spAddLocation(?)`;
    const [rows] = await db
      .promise()
      .query(procedure, [JSON.stringify(payload)]);

    console.log("Add Location:", rows);

    if (rows) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error while creating business", error);
    throw error; // Optionally handle or rethrow the error for further handling
  }
};

const editLocationService = async (payload) => {
  try {
    const procedure = `CALL spEditLocation(?)`;
    const [rows] = await db
      .promise()
      .query(procedure, [JSON.stringify(payload)]);

    console.log("EDIT Location:", rows);

    if (rows) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error while updating location", error);
    throw error; // Optionally handle or rethrow the error for further handling
  }
};

const deleteLocationService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spDeleteLocation(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, [JSON.stringify(payload)]);
    console.log("row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getEventByIdService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spBusinessEventById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const deleteClassbyIdService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spDeleteClassById(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const deleteEventByIdService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spDeleteEventById(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getClassByIdService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spBusinessClassById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

// const getMSG91Service = async (payload) => {
//   const { mobile, otp } = payload; // Destructure payload
//   try {
//     const config = {
//       method: "post",
//       url: `https://control.msg91.com/api/v5/otp?template_id=${template_id}&mobile=91${mobile}&authkey=${auth_key}&otp=${otp}`,
//       headers: {
//         "Content-Type": "application/json",
//       },
//     };

//     const response = await axios(config);
//     const responseData = response.data; // Storing response data
//     console.log("ResponseData::>>", responseData);

//     if (responseData.type === "success") {
//       return { status: true, msg: responseData.type, data: {} };
//     } else {
//       return null; // Do something if the response is not success
//     }
//   } catch (error) {
//     console.log(error); // Handle error if any
//   }
// };

// const verifiedMSG91Service = async (payload) => {
//   const { mobile, otp } = payload; // Destructure payload
//   try {
//     const config = {
//       method: "post",
//       url: `https://control.msg91.com/api/v5/otp?template_id=662fa353d6fc054965185d22&mobile=91${mobile}&authkey=${auth_key}&otp=${otp}`,
//       headers: {
//         "Content-Type": "application/json",
//       },
//     };

//     // Sending HTTP request using axios and awaiting response
//     const response = await axios(config);
//     const responseData = response.data; // Storing response data
//     console.log("ResponseData::>>", responseData);

//     if (responseData.type === "success") {
//       return { status: true, msg: responseData.type, data: {} };
//     } else {
//       return null; // Do something if the response is not success
//     }
//   } catch (error) {
//     console.log(error); // Handle error if any
//   }
// };

const getUserDetailService = async ({ business_id, type }) => {
  try {
    // const parent_id = 5;
    console.log("business_id", business_id);
    const payload = {
      business_id: business_id,
    };
    const checkSQL = `SELECT poc_mobile from business where business_id = ?`;
    const procedure = `CALL spGetBusiness(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("isExist user", row);
    // const payload = {
    //   parent_id: parent_id,
    // };
    // console.log("payload:", payload);
    // const procedure = `CALL spGetUserById(?)`;
    // const row = await db.promise().query(procedure, JSON.stringify(payload)); // Pass parent_id as an array
    // console.log("Row:>>", row);

    if (row) {
      // Check if row is not empty
      return row; // Return the first row of the result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
    throw error; // Rethrow the error to handle it elsewhere if needed
  }
};

const editEventByIdService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spEditEventById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const editClassByIdService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spEditClassById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getAdminDetailsService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spGetAdminDetails(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row:>>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const kycService = async (payload) => {
  try {
    console.log("payload in kycService:>>", payload);
    const procedure = `CALL spSubmitKyc(?)`;
    const row = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const kycdetailsService = async (payload) => {
  try {
    console.log("payload in kycService:>>", payload);
    const procedure = `CALL spGetKycDetails(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const getDetailsService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spGetBusinessDetails(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row:>>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const classListService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spClassListByBusinessId(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row:>>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const createEventService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spCreateEvent(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const createClassService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spCreateClass(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getEventByBusinessIdService = async (payload) => {
  try {
    console.log("Payload in getParentsListService::>>", payload);
    const procedure = `CALL spEventListByBusinessId(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row:>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const categoryListService = async () => {
  try {
    const procedure = `CALL spGetCategories()`;
    const [rows] = await db.promise().query(procedure);

    if (rows.length > 0) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in categoryListService:", error);
    throw error; // Re-throwing the error to be caught by the calling function
  }
};

const getBusinessListService = async (payload) => {
  try {
    const procedure = `CALL spBusinessList()`;
    const [row] = await db.promise().query(procedure);
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getUserByIdService = async (payload) => {
  try {
    console.log("check::>>");
    const procedure = `CALL spBusinessById(?)`;
    const [row] = await db
      .promise()
      .query(procedure, [JSON.stringify(payload)]);
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const totalClassesEventsService = async (payload) => {
  try {
    console.log("check::>>");
    const procedure = `CALL spBusinessClassEventsTotal(?)`;
    const [row] = await db
      .promise()
      .query(procedure, [JSON.stringify(payload)]);
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const editProfileService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spEditBusinessById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getNotificationListService = async (payload) => {
  try {
    const procedure = `CALL spWebNotificationList(?)`;
    const [rows] = await db.promise().query(procedure, JSON.stringify(payload));

    if (rows.length > 0) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in spWebNotificationList:", error);
    throw error; // Re-throwing the error to be caught by the calling function
  }
};

const updateBusinessNotificationService = async (payload) => {
  try {
    const procedure = `CALL spUpdateBusinessNotification(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in updateBusinessNotificationService", error);
    throw error;
  }
};

const updateLastActiveService = async (payload) => {
  try {
    const procedure = `CALL spUpdateLastActive(?)`;
    const [[result]] = await db.promise().query(procedure, JSON.stringify(payload));
    return result;
  } catch (error) {
    console.log("error in updateLastActiveService", error);
    throw error;
  }
};


module.exports = {
  msg91OtpService,
  msg91WhatsappService,
  getEventByBusinessIdService,
  categoryListService,
  createClassService,
  classListService,
  createEventService,
  getClassByIdService,
  getEventByIdService,
  deleteClassbyIdService,
  deleteEventByIdService,
  editEventByIdService,
  editClassByIdService,
  getAdminDetailsService,
  getUserDetailService,
  kycService,
  sendOtpService,
  verifyOtpService,
  getDetailsService,
  getBusinessListService,
  getUserByIdService,
  editProfileService,
  getHomepageImagesService,
  registerBusinessService,
  addLocationService,
  editLocationService,
  totalClassesEventsService,
  deleteLocationService,
  kycdetailsService,
  saveDeviceTokenWebService,
  logOutWebService,
  getNotificationListService,
  updateBusinessNotificationService,
  updateLastActiveService, 
};
