const admin = require("firebase-admin");

const db = require("../config/db");
const axios = require("axios");
const xlsx = require("xlsx");
const { v4: uuidv4 } = require("uuid");

// Initialize Firebase Admin SDK
const serviceAccount = require("../config/firebase.json");
//import db from "../db.js";
//import db from "../config/db.js";
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} = require("@aws-sdk/client-s3");

const s3Client = new S3Client({
  region: "ap-south-1",
  credentials: {
    accessKeyId: "********************",
    secretAccessKey: "sscCud7RMixFfuCqXLey0uKFjrl0oDxvRCeAf9CP",
  },
});

const disableAccountService = async (payload) => {
  try {
    const procedure = `CALL spDisableAccount(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const verifyKycService = async (payload) => {
  try {
    const procedure = `CALL spVerifyKyc(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const suspendparentService = async (payload) => {
  try {
    const procedure = `CALL spSuspendParent(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const sendOtpService = async (payload) => {
  try {
    console.log("payload in otp:>>", payload);
    const procedure = `CALL spSendOtpV1(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const homeService = async () => {
  try {
    const procedure = `Call spGetHomePage()`;
    const [[row]] = await db.promise().query(procedure);

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getRolesListService = async () => {
  try {
    const procedure = `Call spRoleList()`;
    const [row] = await db.promise().query(procedure);
    console.log("Row", row);

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const setAdminRolesService = async (payload) => {
  try {
    const procedure = `CALL spSetAdminRoles(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {}
};

const getBusinessListService = async (payload) => {
  try {
    const procedure = `CALL spBusinessList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    // console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const adminListService = async (payload) => {
  try {
    const procedure = `CALL spAdminList()`;
    const [row] = await db.promise().query(procedure);
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const analyticsService = async (payload) => {
  try {
    const procedure = `CALL spHomePageAnalytics(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const analyticsModuleService = async (payload) => {
  try {
    const procedure = `CALL spAnalytics(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getBusinessByIdService = async (payload) => {
  try {
    const procedure = `CALL spBizByIdAdmin(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {}
};

const getBusinessLocationService = async (payload) => {
  try {
    const procedure = `CALL spAdminGetBusinessLocations(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {}
};

const kycRequestByIdService = async (payload) => {
  try {
    const procedure = `CALL spKycRequestById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {}
};

const eventRequestByIdService = async (payload) => {
  try {
    const procedure = `CALL spSuperAdminEventById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {}
};

const classRequestByIdService = async (payload) => {
  try {
    const procedure = `CALL spSuperAdminClassById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {}
};

const reportedEventByIdService = async (payload) => {
  try {
    const procedure = `CALL spReportedEventById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {}
};

const reportedClassByIdService = async (payload) => {
  try {
    const procedure = `CALL spReportedClassById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {}
};

const getParentByIdService = async (payload) => {
  try {
    const procedure = `CALL spSuperAdminParentById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {}
};

const setHomepageImageService = async (payload) => {
  try {
    const procedure = `CALL spSuperAdminImageSetting(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {}
};

const getAdminByIdService = async (payload) => {
  try {
    const procedure = `CALL spGetAdminByID(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {}
};

const editAdminByIdService = async (payload) => {
  try {
    const procedure = `CALL editAdminById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {}
};

const deleteAdminByIdService = async (admin_id) => {
  try {
    // Ensure admin_id is provided
    if (!admin_id) {
      throw new Error("Admin ID is required.");
    }

    // Prepare the procedure call with parameters
    const procedure = `CALL spDeleteAdminById(?)`;

    // Execute the procedure
    const [results] = await db
      .promise()
      .query(procedure, [JSON.stringify({ admin_id })]);

    // Assuming `results` contains the result set. The structure might differ based on your DB driver.
    if (results && results.affectedRows > 0) {
      return { success: true, message: "Admin successfully deleted." };
    } else {
      return {
        success: false,
        message: "No admin found with the provided ID.",
      };
    }
  } catch (error) {
    // Log the error and provide a meaningful error message
    console.error("Error deleting admin:", error.message);
    return {
      success: false,
      message: "An error occurred while deleting the admin.",
    };
  }
};

const kycListService = async (payload) => {
  try {
    const procedure = `CALL spGetKycList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    // console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const eventListService = async (payload) => {
  try {
    const procedure = `CALL spSuperAdminEventList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const approveRejectEventsService = async (payload) => {
  try {
    const procedure = `CALL spApproveRejectEvent(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const approveRejectClassService = async (payload) => {
  try {
    const procedure = `CALL spApproveRejectClass(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const approveRejectReportedService = async (payload) => {
  try {
    const procedure = `CALL spAdminApproveRejectReports(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row", row);
    console.log("result", row[0]);
    if (row.length > 0) {
      return row[0];
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const approveRejectReportedParentService = async (payload) => {
  try {
    const procedure = `CALL spApproveRejectParentAppeal(?)`;
    const row = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const reportListService = async (payload) => {
  try {
    const procedure = `CALL spBusinessReportsList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const parentsListService = async (payload) => {
  try {
    const procedure = `CALL spSuperAdminParentsList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const exportKycListService = async (payload) => {
  try {
    console.log("Upload::>>", payload);

    const procedure = `CALL spGetKycList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row::>>", row);

    const worksheet = xlsx.utils.json_to_sheet(row[0]);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, "Data");

    const excelBuffer = xlsx.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });
    const random_name = uuidv4();
    console.log("random_name", random_name);
    const s3Params = {
      Bucket: "parenthing",
      Key: `Exports/${random_name}.xlsx`,
      Body: excelBuffer,
      ContentType:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ACL: "public-read",
    };

    const response = await s3Client.send(new PutObjectCommand(s3Params));
    console.log("response", response);
    const file_name = `https://parenthing.s3.ap-south-1.amazonaws.com/Exports/${random_name}.xlsx`;
    if (response) {
      return file_name;
    } else {
      return null;
    }
  } catch (error) {
    console.error("error in app", error);
    return null;
  }
};

// const exportBusinessListService = async (payload) => {
//   try {
//     console.log("Upload::>>", payload);

//     const procedure = `CALL spBusinessList(?)`;
//     const [row] = await db.promise().query(procedure, JSON.stringify(payload));
//     // console.log("Row::>>", row);
//     console.log("📊 Records returned:", row[0] ? row[0].length : 0);;


//     const worksheet = xlsx.utils.json_to_sheet(row[0]);
//     const workbook = xlsx.utils.book_new();
//     xlsx.utils.book_append_sheet(workbook, worksheet, "Data");

//     const excelBuffer = xlsx.write(workbook, {
//       type: "buffer",
//       bookType: "xlsx",
//     });
//     const random_name = uuidv4();
//     console.log("random_name", random_name);
//     const s3Params = {
//       Bucket: "parenthing",
//       Key: `Exports/${random_name}.xlsx`,
//       Body: excelBuffer,
//       ContentType:
//         "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
//       ACL: "public-read",
//     };

//     const response = await s3Client.send(new PutObjectCommand(s3Params));
//     console.log("response", response);
//     const file_name = `https://parenthing.s3.ap-south-1.amazonaws.com/Exports/${random_name}.xlsx`;
//     if (response) {
//       return file_name;
//     } else {
//       return null;
//     }
//   } catch (error) {
//     console.error("error in app", error);
//     return null;
//   }
// };

const exportBusinessListService = async (payload) => {
  try {
    console.log("Upload::>>", payload);

    const procedure = `CALL spBusinessList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("📊 Records returned:", row[0] ? row[0].length : 0);

    // Convert data to CSV format
    let csvContent = '';
    
    if (row[0] && row[0].length > 0) {
      // Get headers from first record
      const headers = Object.keys(row[0][0]);
      csvContent += headers.join(',') + '\n';
      
      // Add data rows
      for (const record of row[0]) {
        const values = headers.map(header => {
          let value = record[header];
          
          // Handle null/undefined values
          if (value === null || value === undefined) {
            value = '';
          }
          
          // Handle nested objects (like location_details) - show only non-empty values
          if (typeof value === 'object' && value !== null) {
            if (Array.isArray(value) && value.length > 0) {
              // For location_details array, extract only non-empty fields
              const obj = value[0];
              const locationParts = [];
              
              if (obj.area && obj.area.trim()) locationParts.push(obj.area.trim());
              if (obj.city && obj.city.trim()) locationParts.push(obj.city.trim());
              if (obj.state && obj.state.trim()) locationParts.push(obj.state.trim());
              if (obj.address && obj.address.trim()) locationParts.push(obj.address.trim());
              if (obj.country && obj.country.trim()) locationParts.push(obj.country.trim());
              if (obj.sub_locality && obj.sub_locality.trim()) locationParts.push(obj.sub_locality.trim());
              
              value = locationParts.join(', ') || ''; // Join with comma or leave blank
            } else {
              value = JSON.stringify(value);
            }
          }
          
          // Convert to string and handle special characters
          value = String(value);
          
          // Simple CSV escaping - wrap in quotes if contains comma, newline, or quote
          if (value.includes(',') || value.includes('\n') || value.includes('"')) {
            // Escape internal quotes by doubling them
            value = value.replace(/"/g, '""');
            value = `"${value}"`;
          }
          
          return value;
        });
        csvContent += values.join(',') + '\n';
      }
    }

    const csvBuffer = Buffer.from(csvContent, 'utf8');
    const random_name = uuidv4();
    console.log("random_name", random_name);
    
    const s3Params = {
      Bucket: "parenthing",
      Key: `Exports/${random_name}.csv`,
      Body: csvBuffer,
      ContentType: "text/csv",
      ACL: "public-read",
    };

    const response = await s3Client.send(new PutObjectCommand(s3Params));
    console.log("response", response);
    const file_name = `https://parenthing.s3.ap-south-1.amazonaws.com/Exports/${random_name}.csv`;
    if (response) {
      return file_name;
    } else {
      return null;
    }
  } catch (error) {
    console.error("error in app", error);
    return null;
  }
};

const exportEventListService = async (payload) => {
  try {
    console.log("Upload::>>", payload);

    const procedure = `CALL spSuperAdminEventList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row::>>", row);

    const worksheet = xlsx.utils.json_to_sheet(row[0]);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, "Data");

    const excelBuffer = xlsx.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });
    const random_name = uuidv4();
    console.log("random_name", random_name);
    const s3Params = {
      Bucket: "parenthing",
      Key: `Exports/${random_name}.xlsx`,
      Body: excelBuffer,
      ContentType:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ACL: "public-read",
    };

    const response = await s3Client.send(new PutObjectCommand(s3Params));
    console.log("response", response);
    const file_name = `https://parenthing.s3.ap-south-1.amazonaws.com/Exports/${random_name}.xlsx`;
    if (response) {
      return file_name;
    } else {
      return null;
    }
  } catch (error) {
    console.error("error in app", error);
    return null;
  }
};

const reportedParentsListService = async (payload) => {
  try {
    const procedure = `CALL spGetReportedParents(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const exportClassListService = async (payload) => {
  try {
    console.log("Upload::>>", payload);

    const procedure = `CALL spSuperAdminClassList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row::>>", row);

    const worksheet = xlsx.utils.json_to_sheet(row[0]);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, "Data");

    const excelBuffer = xlsx.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });
    const random_name = uuidv4();
    console.log("random_name", random_name);
    const s3Params = {
      Bucket: "parenthing",
      Key: `Exports/${random_name}.xlsx`,
      Body: excelBuffer,
      ContentType:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ACL: "public-read",
    };

    const response = await s3Client.send(new PutObjectCommand(s3Params));
    console.log("response", response);
    const file_name = `https://parenthing.s3.ap-south-1.amazonaws.com/Exports/${random_name}.xlsx`;
    if (response) {
      return file_name;
    } else {
      return null;
    }
  } catch (error) {
    console.error("error in app", error);
    return null;
  }
};

const exportReportListService = async (payload) => {
  try {
    console.log("Upload::>>", payload);

    const procedure = `CALL spBusinessReportsList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row::>>", row);

    const worksheet = xlsx.utils.json_to_sheet(row[0]);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, "Data");

    const excelBuffer = xlsx.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });
    const random_name = uuidv4();
    console.log("random_name", random_name);
    const s3Params = {
      Bucket: "parenthing",
      Key: `Exports/${random_name}.xlsx`,
      Body: excelBuffer,
      ContentType:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ACL: "public-read",
    };

    const response = await s3Client.send(new PutObjectCommand(s3Params));
    console.log("response", response);
    const file_name = `https://parenthing.s3.ap-south-1.amazonaws.com/Exports/${random_name}.xlsx`;
    if (response) {
      return file_name;
    } else {
      return null;
    }
  } catch (error) {
    console.error("error in app", error);
    return null;
  }
};

const exportParentListService = async (payload) => {
  try {
    console.log("Upload::>>", payload);

    const procedure = `CALL spSuperAdminParentsList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row::>>", row);

    const worksheet = xlsx.utils.json_to_sheet(row[0]);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, "Data");

    const excelBuffer = xlsx.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });
    const random_name = uuidv4();
    console.log("random_name", random_name);
    const s3Params = {
      Bucket: "parenthing",
      Key: `Exports/${random_name}.xlsx`,
      Body: excelBuffer,
      ContentType:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ACL: "public-read",
    };

    const response = await s3Client.send(new PutObjectCommand(s3Params));
    console.log("response", response);
    const file_name = `https://parenthing.s3.ap-south-1.amazonaws.com/Exports/${random_name}.xlsx`;
    if (response) {
      return file_name;
    } else {
      return null;
    }
  } catch (error) {
    console.error("error in app", error);
    return null;
  }
};

const inactiveParentsService = async (payload) => {
  try {
    const procedure = `CALL spSuperAdminInactiveParents(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const exportInactiveParentsService = async (payload) => {
  try {
    console.log("Upload::>>", payload);

    const procedure = `CALL spSuperAdminInactiveParents(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row::>>", row);

    const worksheet = xlsx.utils.json_to_sheet(row[0]);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, "Data");

    const excelBuffer = xlsx.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });
    const random_name = uuidv4();
    console.log("random_name", random_name);
    const s3Params = {
      Bucket: "parenthing",
      Key: `Exports/${random_name}.xlsx`,
      Body: excelBuffer,
      ContentType:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ACL: "public-read",
    };

    const response = await s3Client.send(new PutObjectCommand(s3Params));
    console.log("response", response);
    const file_name = `https://parenthing.s3.ap-south-1.amazonaws.com/Exports/${random_name}.xlsx`;
    if (response) {
      return file_name;
    } else {
      return null;
    }
  } catch (error) {
    console.error("error in app", error);
    return null;
  }
};

const exportReportedParentsListService = async (payload) => {
  try {
    console.log("Upload::>>", payload);

    const procedure = `CALL spGetReportedParents(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row::>>", row);

    const worksheet = xlsx.utils.json_to_sheet(row[0]);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, "Data");

    const excelBuffer = xlsx.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });
    const random_name = uuidv4();
    console.log("random_name", random_name);
    const s3Params = {
      Bucket: "parenthing",
      Key: `Exports/${random_name}.xlsx`,
      Body: excelBuffer,
      ContentType:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ACL: "public-read",
    };

    const response = await s3Client.send(new PutObjectCommand(s3Params));
    console.log("response", response);
    const file_name = `https://parenthing.s3.ap-south-1.amazonaws.com/Exports/${random_name}.xlsx`;
    if (response) {
      return file_name;
    } else {
      return null;
    }
  } catch (error) {
    console.error("error in app", error);
    return null;
  }
};

const classListService = async (payload) => {
  try {
    const procedure = `CALL spSuperAdminClassList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getFirebaseNotification = async (user_id, title, message, type) => {
  const sql = `SELECT firebase_id AS fire_base_id FROM Users WHERE user_id = ?`;
  const [[user]] = await db.promise().query(sql, user_id);

  if (!user.fire_base_id) {
    console.log("token is not found", user);
    return;
  }
  if (user && user.fire_base_id) {
    console.log("user", user);
  }
  console.log("sendReminderNotification running");

  const data = {
    title: title,
    body: message,
    notification_type: type, // Example type, adjust as needed
  };

  try {
    const messagePayload = {
      data: data,
      apns: {
        headers: {
          "apns-priority": "10",
        },
        payload: {
          aps: {
            badge: 0,
            sound: "default",
            alert: {
              title: data.title,
              body: data.body,
              type: data.notification_type,
            },
          },
        },
        fcm_options: {
          image:
            "https://parenthing.s3.ap-south-1.amazonaws.com/dummy/banner.png",
        },
      },
      token: user.fire_base_id,
    };

    console.log("messagePayload", messagePayload);
    const msg = await admin.messaging().send(messagePayload);
    console.log("messge send to ", user);

    console.log("Successfully sent message:", msg);

    return msg;
  } catch (error) {
    console.log("Error sending message:", error);
  }
};

const deleteEventService = async (payload) => {
  try {
    const { event_id } = payload;
    
    // Direct DELETE query - cascade will handle related tables
    const deleteSQL = `DELETE FROM events WHERE event_id = ?`;
    const [result] = await db.promise().query(deleteSQL, [event_id]);
    
    console.log("Delete result:", result);
    
    if (result.affectedRows > 0) {
      return { success: true, message: "Event deleted successfully" };
    } else {
      return { success: false, message: "Event not found" };
    }
  } catch (error) {
    console.log("Error in deleteEventService:", error);
    throw error;
  }
};

const deleteClassService = async (payload) => {
  try {
    const { class_id } = payload;
    
    // Direct DELETE query - cascade will handle related tables
    const deleteSQL = `DELETE FROM classes WHERE class_id = ?`;
    const [result] = await db.promise().query(deleteSQL, [class_id]);
    
    console.log("Delete result:", result);
    
    if (result.affectedRows > 0) {
      return { success: true, message: "Class deleted successfully" };
    } else {
      return { success: false, message: "Class not found" };
    }
  } catch (error) {
    console.log("Error in deleteClassService:", error);
    throw error;
  }
};

const deleteBusinessService = async (payload) => {
  try {
    const { business_id } = payload;
    
    // Direct DELETE query - cascade will handle all related tables
    const deleteSQL = `DELETE FROM business WHERE business_id = ?`;
    const [result] = await db.promise().query(deleteSQL, [business_id]);
    
    console.log("Delete result:", result);
    
    if (result.affectedRows > 0) {
      return { success: true, message: "Business deleted successfully" };
    } else {
      return { success: false, message: "Business not found" };
    }
  } catch (error) {
    console.log("Error in deleteBusinessService:", error);
    throw error;
  }
};

const deleteParentService = async (payload) => {
  try {
    const { parent_id } = payload;
    
    // Direct DELETE query - cascade will handle all related tables
    const deleteSQL = `DELETE FROM Parents WHERE parent_id = ?`;
    const [result] = await db.promise().query(deleteSQL, [parent_id]);
    
    console.log("Delete result:", result);
    
    if (result.affectedRows > 0) {
      return { success: true, message: "Parent deleted successfully" };
    } else {
      return { success: false, message: "Parent not found" };
    }
  } catch (error) {
    console.log("Error in deleteParentService:", error);
    throw error;
  }
};


module.exports = {
  disableAccountService,
  getRolesListService,
  exportReportedParentsListService,
  exportInactiveParentsService,
  exportReportListService,
  exportParentListService,
  exportClassListService,
  exportEventListService,
  exportKycListService,
  approveRejectEventsService,
  approveRejectClassService,
  adminListService,
  analyticsService,
  parentsListService,
  classRequestByIdService,
  reportedEventByIdService,
  reportedClassByIdService,
  kycRequestByIdService,
  getBusinessByIdService,
  eventRequestByIdService,
  reportListService,
  eventListService,
  classListService,
  kycListService,
  homeService,
  verifyKycService,
  sendOtpService,
  getBusinessListService,
  inactiveParentsService,
  reportedParentsListService,
  getParentByIdService,
  suspendparentService,
  getAdminByIdService,
  exportBusinessListService,
  editAdminByIdService,
  setHomepageImageService,
  analyticsModuleService,
  approveRejectReportedService,
  approveRejectReportedParentService,
  getBusinessLocationService,
  setAdminRolesService,
  deleteAdminByIdService,
  getFirebaseNotification,
  deleteEventService,
  deleteClassService,
  deleteBusinessService,
  deleteParentService,
};
