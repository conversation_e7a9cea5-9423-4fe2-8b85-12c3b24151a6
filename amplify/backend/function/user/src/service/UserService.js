const db = require("../config/db");
const axios = require("axios");
const { admin } = require("../utility/firebase-config");
const crypto = require("crypto");
const ENCRYPTION_KEY = "wwwwwwwwwwwwwwweeeeeeeeeeeeeeeee"; // Must be 256 bits (32 characters)
const IV_LENGTH = 16; // For AES, this is always 16
const MSG91_AUTH_KEY = "420745AQofv3Fn66332257P1";


const msg91OtpService = async (mobile, otp, templateId) => {
  try {
    if (process.env.ENV !== 'production') {
      console.log(`OTP disabled in ${process.env.ENV} environment`);
      return { status: true, msg: 'OTP disabled in staging', data: {} };
    }
    
    // For OTP sending
    const otpConfig = {
      method: "post",
      url: `https://control.msg91.com/api/v5/otp`,
      headers: {
        "Content-Type": "application/json",
        "authkey": MSG91_AUTH_KEY
      },
      data: {
        mobile: `91${mobile}`,
        template_id: templateId,
        otp: otp,
        otp_expiry: 10 // In minutes
      }
    };

    const response = await axios(otpConfig);
    const responseData = response.data;
    console.log("MSG91 OTP Response:", responseData);

    if (responseData.type === "success") {
      return { status: true, msg: responseData.type, data: {} };
    } else {
      return { status: false, msg: "Failed to send OTP", data: responseData };
    }
  } catch (error) {
    console.log("MSG91 OTP Error:", error);
    return { status: false, msg: "Error sending OTP", data: {} };
  }
};

const msg91WhatsappService = async (mobile, templateName, bodyValues = {}) => {
  try {
    // Only work in production environment
    if (process.env.ENV !== 'production') {
      console.log(`WhatsApp disabled in ${process.env.ENV} environment`);
      return { status: true, msg: 'WhatsApp disabled in staging', data: {} };
    }
    
    // Get OTP value from bodyValues
    const otpValue = bodyValues.OTP || bodyValues.otp || '';
    
    const whatsappData = {
      integrated_number: "918530572636",
      content_type: "template",
      payload: {
        messaging_product: "whatsapp",
        type: "template",
        template: {
          name: templateName,
          language: {
            code: "en",
            policy: "deterministic"
          },
          namespace: "78dd51ad_cb40_4786_b158_e43e2fc14a36",
          to_and_components: [
            {
              to: [`91${mobile}`],
              components: {
                body_1: {
                  type: "text",
                  value: otpValue
                },
                button_1: {
                  type: "text",
                  subtype: "url",
                  value: otpValue // Using same OTP value for button
                }
              }
            }
          ]
        }
      }
    };

    const options = {
      method: 'POST',
      url: 'https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/',
      headers: {
        'Content-Type': 'application/json',
        'authkey': MSG91_AUTH_KEY
      },
      data: whatsappData
    };

    console.log('WhatsApp Data:', JSON.stringify(whatsappData, null, 2));
    
    const response = await axios(options);
    console.log('WhatsApp Response:', response.data);
    
    return { 
      status: true, 
      msg: 'WhatsApp sent successfully', 
      data: response.data 
    };
  } catch (error) {
    console.error('WhatsApp Error:', error.response?.data || error.message);
    return { 
      status: false, 
      msg: 'Error sending WhatsApp', 
      data: error.response?.data || {} 
    };
  }
};

const sendOtpService = async (payload) => {
  try {
    console.log("payload in otp:>>", payload);
    const procedure = `CALL spSendOtpV1(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const logOutService = async (payload) => {
  try {
    console.log("payload in otp:>>", payload);
    const procedure = `CALL spLogOut(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    // console.log("row::>>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const verifyOtpService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spverifyOtp(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const parentAppealService = async (payload) => {
  try {
    console.log("Payload in Parent Appeal::>>", payload);
    const procedure = `CALL spParentsAppeal(?)`;
    const row = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Service payload??::>>", row);

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error ", error);
  }
};

const getRegisterUserDetailsService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spRegisterParent(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const editUserDetailsService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spEditParent(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const trackerService = async (payload) => {
  try {
    const procedure = `CALL spInterestTracker(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const getUserDetailService = async ({ parent_id, type }) => {
  try {
    // const parent_id = 5;
    console.log("parent_id", parent_id);
    const payload = {
      parent_id: parent_id,
    };
    const checkSQL = `SELECT mobile from Parents where parent_id = ?`;
    const procedure = `CALL spGetUserById(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("isExist user", row);
    // const payload = {
    //   parent_id: parent_id,
    // };
    // console.log("payload:", payload);
    // const procedure = `CALL spGetUserById(?)`;
    // const row = await db.promise().query(procedure, JSON.stringify(payload)); // Pass parent_id as an array
    // console.log("Row:>>", row);

    if (row) {
      // Check if row is not empty
      return row; // Return the first row of the result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
    throw error; // Rethrow the error to handle it elsewhere if needed
  }
};

const parentByIdService = async (payload) => {
  try {
    // const parent_id = 5;
    console.log("payload", payload);

    const procedure = `CALL spParentById(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("isExist user", row);
    // const payload = {
    //   parent_id: parent_id,
    // };
    // console.log("payload:", payload);
    // const procedure = `CALL spGetUserById(?)`;
    // const row = await db.promise().query(procedure, JSON.stringify(payload)); // Pass parent_id as an array
    // console.log("Row:>>", row);

    if (row) {
      // Check if row is not empty
      return row; // Return the first row of the result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
    throw error; // Rethrow the error to handle it elsewhere if needed
  }
};

const getDetailsService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spGetDetails(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row:>>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getUserDetailsService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spProfileDetails(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getConnectionListService = async (payload) => {
  try {
    console.log("Payload in getConnectionListService::>>", payload);
    const procedure = `CALL spConnectionList(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));
    
    // Default response structure
    const response = {
      connections: [],
      meta: {
        total_unread_messages: 0
      }
    };

    // Check if results exist and have proper structure
    if (results && Array.isArray(results)) {
      // First result set contains connections list
      if (results[0] && Array.isArray(results[0])) {
        response.connections = results[0];
      }

     // Second result set contains total unread messages count
      if (results[1] && Array.isArray(results[1]) && results[1][0]) {
        const totalUnread = results[1][0].total_unread_messages;
        // console.log("Total unread value:", totalUnread, "Type:", typeof totalUnread);
        
        // Handle both string and number types
        if (typeof totalUnread === 'string') {
          response.meta.total_unread_messages = parseInt(totalUnread, 10) || 0;
          // console.log("Converted string to number:", response.meta.total_unread_messages);
        } else if (typeof totalUnread === 'number') {
          response.meta.total_unread_messages = totalUnread;
          // console.log("Using number directly:", response.meta.total_unread_messages);
        }
      }
    }

    console.log("Final response:", response);

    return response;
  } catch (error) {
    console.error("Error in getConnectionListService:", error);
    throw error;
  }
};

const getParentsListService = async (payload) => {
  try {
    console.log("Payload in getParentsListService::>>", payload);
    const procedure = `CALL spParentsList(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("Row:>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const bookmarkListService = async (payload) => {
  try {
    console.log("Payload in getParentsListService::>>", payload);
    const procedure = `CALL spBookmarkList(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("Row:>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getConnectionByIdService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spConnectionById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const deleteUserService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spDeleteAccount(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const saveDeviceTokenService = async (payload) => {
  try {
    const procedure = `CALL spSaveDeviceToken(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("row", row[0]);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error", error);
  }
};

const ConnectionRequestService = async (payload) => {
  try {
    console.log("payload::>>", payload);
    const procedure = `CALL spConnectionRequest(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const BookmarkService = async (payload) => {
  try {
    const procedure = `CALL spManageBookmark(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const categoryListService = async () => {
  try {
    const procedure = `CALL spGetCategories()`;
    const [rows] = await db.promise().query(procedure);

    if (rows.length > 0) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in categoryListService:", error);
    throw error; // Re-throwing the error to be caught by the calling function
  }
};

const globalSearchService = async (payload) => {
  try {
    const procedure = `CALL spGlobalSearch(?)`;
    const [[rows]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("Rows::>>", rows);
    if (rows.length > 0) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in globalSearchService:", error);
    throw error; // Re-throwing the error to be caught by the calling function
  }
};

const getCityListService = async () => {
  try {
    const procedure = `CALL spGetServiceableList()`;
    const [rows] = await db.promise().query(procedure);

    if (rows.length > 0) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in categoryListService:", error);
    throw error; // Re-throwing the error to be caught by the calling function
  }
};

const interestsCategoryService = async () => {
  try {
    const procedure = `CALL spGetCategoryWithSubcategories()`;
    const [rows] = await db.promise().query(procedure);

    if (rows.length > 0) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in categoryListService:", error);
    throw error; // Re-throwing the error to be caught by the calling function
  }
};

const reportEventsAndClassesService = async (payload) => {
  try {
    const procedure = `CALL spReportEventsClasses(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const disableAccountService = async (payload) => {
  try {
    const procedure = `CALL spDisableAccount(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const deleteRequestService = async (payload) => {
  try {
    const procedure = `CALL spDeleteAccountRequests(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const reportEventsService = async (payload) => {
  try {
    const procedure = `CALL spReportEvents(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const reportClassService = async (payload) => {
  try {
    const procedure = `CALL spReportClasses(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const reportParentsService = async (payload) => {
  try {
    const procedure = `CALL spReportParents(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const getChatHistoryService = async (payload) => {
  try {
    const procedure = `CALL spGetChatHistory(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return { data: row[0], meta: row[1] };
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const getNotificationListService = async (payload) => {
  try {
    const procedure = `CALL spAppNotificationList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const updateNotificationService = async (payload) => {
  try {
    const procedure = `CALL spUpdateNotification(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in updateNotificationService", error);
    throw error;
  }
};

const getFirebaseNotificationService = async (payload) => {
  const { receiver_id, data } = payload;
  console.log("payload:-", payload);
  try {
    const sql = `select firebase_id as fire_base_id from Users where parent_id = ?`;
    const [[row]] = await db.promise().query(sql, receiver_id);
   // if (row != null || row != undefined) {
    if ((row && row.fire_base_id)) {
      console.log("row", row);
      const message = {
        data: data,
        apns: {
          headers: {
            "apns-priority": "10",
          },
          payload: {
            aps: {
              badge: 0,
              sound: "default",
              alert: {
                title: data.title,
                body: data.body,
                type: data.notification_type,
              },
            },
          },
          fcm_options: {
            image:
              "https://parenthing.s3.ap-south-1.amazonaws.com/dummy/banner.png",
          },
        },
        token: row.fire_base_id,
      };
      console.log("message", message);
      const msg = await admin.messaging().send(message);
      console.log("msg", msg);
      if (msg) {
        return msg;
      } else {
        return null;
      }
    } else {
      console.log("No firebase id");
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const encrypt = (text) => {
  let iv = crypto.randomBytes(IV_LENGTH);
  let cipher = crypto.createCipheriv(
    "aes-256-cbc",
    Buffer.from(ENCRYPTION_KEY),
    iv
  );
  let encrypted = cipher.update(text);

  encrypted = Buffer.concat([encrypted, cipher.final()]);

  return iv.toString("hex") + ":" + encrypted.toString("hex");
};

const decrypt = async (text) => {
  return new Promise((resolve, reject) => {
    try {
      let textParts = text.split(":");
      let iv = Buffer.from(textParts.shift(), "hex");
      let encryptedText = Buffer.from(textParts.join(":"), "hex");
      let decipher = crypto.createDecipheriv(
        "aes-256-cbc",
        Buffer.from(ENCRYPTION_KEY),
        iv
      );
      let decrypted = decipher.update(encryptedText);

      decrypted = Buffer.concat([decrypted, decipher.final()]);

      resolve(decrypted.toString());
    } catch (error) {
      reject(error);
    }
  });
};

const updateLastActiveService = async (payload) => {
  try {
    const procedure = `CALL spUpdateLastActive(?)`;
    const [[result]] = await db.promise().query(procedure, JSON.stringify(payload));
    return result;
  } catch (error) {
    console.log("error in updateLastActiveService", error);
    throw error;
  }
};

const markMessagesAsReadService = async (payload) => {
  try {
    console.log("Payload in markMessagesAsReadService::>>", payload);
    const procedure = `CALL spMarkMessagesAsRead(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row:>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in markMessagesAsReadService", error);
    throw error;
  }
};


module.exports = {
  msg91OtpService,
  msg91WhatsappService,
  trackerService,
  logOutService,
  getFirebaseNotificationService,
  deleteRequestService,
  parentByIdService,
  globalSearchService,
  getChatHistoryService,
  disableAccountService,
  reportParentsService,
  getCityListService,
  reportEventsService,
  reportClassService,
  sendOtpService,
  verifyOtpService,
  getUserDetailService,
  getRegisterUserDetailsService,
  getDetailsService,
  editUserDetailsService,
  getUserDetailsService,
  getConnectionListService,
  getConnectionByIdService,
  saveDeviceTokenService,
  getParentsListService,
  deleteUserService,
  ConnectionRequestService,
  BookmarkService,
  bookmarkListService,
  categoryListService,
  interestsCategoryService,
  reportEventsAndClassesService,
  parentAppealService,
  getNotificationListService,
  updateNotificationService,
  encrypt,
  decrypt,
  updateLastActiveService,
  markMessagesAsReadService,
};
