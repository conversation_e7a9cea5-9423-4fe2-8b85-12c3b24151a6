const db = require("../config/db");
const axios = require("axios");
const apiSecret = "0a5242c774f64eff9987a715f9c26f2b";
const apiKey = "61a8770eae28640004506117";
const channelId = "6125edb82968810004894981";
const { admin } = require("../utility/firebase-config");
const crypto = require("crypto");
const ENCRYPTION_KEY = "wwwwwwwwwwwwwwweeeeeeeeeeeeeeeee"; // Must be 256 bits (32 characters)
const IV_LENGTH = 16; // For AES, this is always 16


const msg91OtpService = async (mobile, otp, templateId) => {
  try {
    // Only work in production environment
    if (process.env.ENV !== 'production') {
      console.log(`OTP disabled in ${process.env.ENV} environment`);
      return { status: true, msg: 'OTP disabled in staging', data: {} };
    }

    const auth_key = "420745AQofv3Fn66332257P1"; // From categoryService.js
    
    const config = {
      method: "post",
      url: `https://control.msg91.com/api/v5/otp?template_id=${templateId}&mobile=91${mobile}&authkey=${auth_key}&otp=${otp}`,
      headers: {
        "Content-Type": "application/json",
      },
    };

    const response = await axios(config);
    const responseData = response.data;
    console.log("MSG91 OTP Response:", responseData);

    if (responseData.type === "success") {
      return { status: true, msg: responseData.type, data: {} };
    } else {
      return { status: false, msg: "Failed to send OTP", data: responseData };
    }
  } catch (error) {
    console.log("MSG91 OTP Error:", error);
    return { status: false, msg: "Error sending OTP", data: {} };
  }
};

const msg91WhatsappService = async (mobile, templateName, bodyValues = {}) => {
  try {
    // Only work in production environment
    if (process.env.ENV !== 'production') {
      console.log(`WhatsApp disabled in ${process.env.ENV} environment`);
      return { status: true, msg: 'WhatsApp disabled in staging', data: {} };
    }

    const MSG91_AUTH_KEY = '420745AQofv3Fn66332257P1';
    
    // Prepare components based on bodyValues
    const components = {};
    Object.keys(bodyValues).forEach((key, index) => {
      components[`body_${index + 1}`] = {
        type: "text",
        value: bodyValues[key]
      };
    });

    const whatsappData = {
      integrated_number: "918530572636",
      content_type: "template",
      payload: {
        messaging_product: "whatsapp",
        type: "template",
        template: {
          name: templateName,
          language: {
            code: "en",
            policy: "deterministic"
          },
          namespace: "78dd51ad_cb40_4786_b158_e43e2fc14a36",
          to_and_components: [
            {
              to: [`91${mobile}`],
              components: components
            }
          ]
        }
      }
    };

    const options = {
      method: 'POST',
      url: 'https://api.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/',
      headers: {
        'Content-Type': 'application/json',
        'authkey': MSG91_AUTH_KEY
      },
      data: whatsappData
    };

    console.log('WhatsApp Data:', JSON.stringify(whatsappData, null, 2));
    
    const response = await axios(options);
    console.log('WhatsApp Response:', response.data);
    
    return { 
      status: true, 
      msg: 'WhatsApp sent successfully', 
      data: response.data 
    };
  } catch (error) {
    console.error('WhatsApp Error:', error.response?.data || error.message);
    return { 
      status: false, 
      msg: 'Error sending WhatsApp', 
      data: error.response?.data || {} 
    };
  }
};

// const getWhatsappService = async (payload) => {
//   const { data, type, template_name, body_values } = payload;
//   console.log("payload in getWhatsappService", payload);
//   try {
//     // const temp = `select parent_name as user_name, mobile from OTP where id = ?`;
//     // const user = `select parent_name as user_name, mobile from users where id = ?`;
//     // const sql = type == "temp" ? temp : user;
//     // console.log("sql", sql);
//     // const [[row]] = await db.promise().query(sql, user_id);
//     // console.log("whatsapp row", row);
//     // const name =
//     //   data.name != null || data.name != undefined ? data.name : row.user_name;
//     let payloaddata = {};
//     console.log("body values:>>", body_values);
//     payloaddata = {
//       channelId: "6125edb82968810004894981",
//       channelType: "whatsapp",
//       recipient: {
//         name: "Parenthing User",
//         phone:
//           data.mobile != null || data.mobile != undefined
//             ? `91${data.mobile}`
//             : `91${row.mobile}`,
//       },
//       whatsapp: {
//         type: "template",
//         template: {
//           templateName: "parenthing_otp_testv3",
//           bodyValues: {
//             OTP: body_values.OTP,
//           },
//         },
//       },
//     };

//     console.log("payloaddata", payloaddata.whatsapp.template.bodyValues);
//     const options = {
//       method: "POST",
//       url: `https://server.gallabox.com/devapi/messages/whatsapp`,
//       headers: {
//         // apiSecret: process.env.WAAPISECRET,
//         // apiKey: process.env.WAAPIKEY,
//         apiSecret: apiSecret,
//         apiKey: apiKey,
//         "Content-Type": "application/json",
//       },
//       data: payloaddata,
//     };
//     console.log("options::>>", options);
//     const result = await axios(options);
//     console.log("result", result.data);

//     if (result.data.status === "ACCEPTED") {
//       return { status: true, msg: result.data.message, data: {} };
//     } else {
//       return null;
//     }
//   } catch (error) {
//     console.log("error in getWhatsappService app", error);
//   }
// };

// const getMSG91Service = async (payload) => {
//   const { mobile, otp } = payload; // Destructure payload
//   try {
//     const config = {
//       method: "post",
//       url: `https://control.msg91.com/api/v5/otp?template_id=${template_id}&mobile=91${mobile}&authkey=${auth_key}&otp=${otp}`,
//       headers: {
//         "Content-Type": "application/json",
//       },
//     };

//     const response = await axios(config);
//     const responseData = response.data; // Storing response data
//     console.log("ResponseData::>>", responseData);

//     if (responseData.type === "success") {
//       return { status: true, msg: responseData.type, data: {} };
//     } else {
//       return null; // Do something if the response is not success
//     }
//   } catch (error) {
//     console.log(error); // Handle error if any
//   }
// };

// const verifiedMSG91Service = async (payload) => {
//   const { mobile, otp } = payload; // Destructure payload
//   try {
//     const config = {
//       method: "post",
//       url: `https://control.msg91.com/api/v5/otp?template_id=662fa353d6fc054965185d22&mobile=91${mobile}&authkey=${auth_key}&otp=${otp}`,
//       headers: {
//         "Content-Type": "application/json",
//       },
//     };

//     // Sending HTTP request using axios and awaiting response
//     const response = await axios(config);
//     const responseData = response.data; // Storing response data
//     console.log("ResponseData::>>", responseData);

//     if (responseData.type === "success") {
//       return { status: true, msg: responseData.type, data: {} };
//     } else {
//       return null; // Do something if the response is not success
//     }
//   } catch (error) {
//     console.log(error); // Handle error if any
//   }
// };

const sendOtpService = async (payload) => {
  try {
    console.log("payload in otp:>>", payload);
    const procedure = `CALL spSendOtpV1(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const logOutService = async (payload) => {
  try {
    console.log("payload in otp:>>", payload);
    const procedure = `CALL spLogOut(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    // console.log("row::>>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const verifyOtpService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spverifyOtp(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const parentAppealService = async (payload) => {
  try {
    console.log("Payload in Parent Appeal::>>", payload);
    const procedure = `CALL spParentsAppeal(?)`;
    const row = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Service payload??::>>", row);

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error ", error);
  }
};

const getRegisterUserDetailsService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spRegisterParent(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const editUserDetailsService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spEditParent(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const trackerService = async (payload) => {
  try {
    const procedure = `CALL spInterestTracker(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const getUserDetailService = async ({ parent_id, type }) => {
  try {
    // const parent_id = 5;
    console.log("parent_id", parent_id);
    const payload = {
      parent_id: parent_id,
    };
    const checkSQL = `SELECT mobile from Parents where parent_id = ?`;
    const procedure = `CALL spGetUserById(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("isExist user", row);
    // const payload = {
    //   parent_id: parent_id,
    // };
    // console.log("payload:", payload);
    // const procedure = `CALL spGetUserById(?)`;
    // const row = await db.promise().query(procedure, JSON.stringify(payload)); // Pass parent_id as an array
    // console.log("Row:>>", row);

    if (row) {
      // Check if row is not empty
      return row; // Return the first row of the result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
    throw error; // Rethrow the error to handle it elsewhere if needed
  }
};

const parentByIdService = async (payload) => {
  try {
    // const parent_id = 5;
    console.log("payload", payload);

    const procedure = `CALL spParentById(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("isExist user", row);
    // const payload = {
    //   parent_id: parent_id,
    // };
    // console.log("payload:", payload);
    // const procedure = `CALL spGetUserById(?)`;
    // const row = await db.promise().query(procedure, JSON.stringify(payload)); // Pass parent_id as an array
    // console.log("Row:>>", row);

    if (row) {
      // Check if row is not empty
      return row; // Return the first row of the result set
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
    throw error; // Rethrow the error to handle it elsewhere if needed
  }
};

const getDetailsService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spGetDetails(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row:>>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getUserDetailsService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spProfileDetails(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getConnectionListService = async (payload) => {
  try {
    console.log("Payload in getConnectionListService::>>", payload);
    const procedure = `CALL spConnectionList(?)`;
    const [results] = await db.promise().query(procedure, JSON.stringify(payload));
    
    // First result set contains connections list
    // Second result set contains total unread messages count
    if (results && results.length >= 2) {
      return {
        connections: results[0],
        meta: {
          total_unread_messages: results[1][0].total_unread_messages
        }
      };
    }
    return null;
  } catch (error) {
    console.log("Error in getConnectionListService:", error);
    throw error;
  }
};

const getParentsListService = async (payload) => {
  try {
    console.log("Payload in getParentsListService::>>", payload);
    const procedure = `CALL spParentsList(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("Row:>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const bookmarkListService = async (payload) => {
  try {
    console.log("Payload in getParentsListService::>>", payload);
    const procedure = `CALL spBookmarkList(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("Row:>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getConnectionByIdService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spConnectionById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const deleteUserService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spDeleteAccount(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const saveDeviceTokenService = async (payload) => {
  try {
    const procedure = `CALL spSaveDeviceToken(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("row", row[0]);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error", error);
  }
};

const ConnectionRequestService = async (payload) => {
  try {
    console.log("payload::>>", payload);
    const procedure = `CALL spConnectionRequest(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const BookmarkService = async (payload) => {
  try {
    const procedure = `CALL spManageBookmark(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const categoryListService = async () => {
  try {
    const procedure = `CALL spGetCategories()`;
    const [rows] = await db.promise().query(procedure);

    if (rows.length > 0) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in categoryListService:", error);
    throw error; // Re-throwing the error to be caught by the calling function
  }
};

const globalSearchService = async (payload) => {
  try {
    const procedure = `CALL spGlobalSearch(?)`;
    const [[rows]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("Rows::>>", rows);
    if (rows.length > 0) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in globalSearchService:", error);
    throw error; // Re-throwing the error to be caught by the calling function
  }
};

const getCityListService = async () => {
  try {
    const procedure = `CALL spGetServiceableList()`;
    const [rows] = await db.promise().query(procedure);

    if (rows.length > 0) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in categoryListService:", error);
    throw error; // Re-throwing the error to be caught by the calling function
  }
};

const interestsCategoryService = async () => {
  try {
    const procedure = `CALL spGetCategoryWithSubcategories()`;
    const [rows] = await db.promise().query(procedure);

    if (rows.length > 0) {
      return rows;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in categoryListService:", error);
    throw error; // Re-throwing the error to be caught by the calling function
  }
};

const reportEventsAndClassesService = async (payload) => {
  try {
    const procedure = `CALL spReportEventsClasses(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const disableAccountService = async (payload) => {
  try {
    const procedure = `CALL spDisableAccount(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const deleteRequestService = async (payload) => {
  try {
    const procedure = `CALL spDeleteAccountRequests(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const reportEventsService = async (payload) => {
  try {
    const procedure = `CALL spReportEvents(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const reportClassService = async (payload) => {
  try {
    const procedure = `CALL spReportClasses(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const reportParentsService = async (payload) => {
  try {
    const procedure = `CALL spReportParents(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const getChatHistoryService = async (payload) => {
  try {
    const procedure = `CALL spGetChatHistory(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return { data: row[0], meta: row[1] };
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const getNotificationListService = async (payload) => {
  try {
    const procedure = `CALL spAppNotificationList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const updateNotificationService = async (payload) => {
  try {
    const procedure = `CALL spUpdateNotification(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in updateNotificationService", error);
    throw error;
  }
};

const getFirebaseNotificationService = async (payload) => {
  const { receiver_id, data } = payload;
  console.log("payload:-", payload);
  try {
    const sql = `select firebase_id as fire_base_id from Users where parent_id = ?`;
    const [[row]] = await db.promise().query(sql, receiver_id);
   // if (row != null || row != undefined) {
    if ((row && row.fire_base_id)) {
      console.log("row", row);
      const message = {
        data: data,
        apns: {
          headers: {
            "apns-priority": "10",
          },
          payload: {
            aps: {
              badge: 0,
              sound: "default",
              alert: {
                title: data.title,
                body: data.body,
              },
            },
            title: data.title,
            body: data.body,
            type: data.notification_type,
          },
          fcm_options: {
            image:
              "https://parenthing.s3.ap-south-1.amazonaws.com/dummy/banner.png",
          },
        },
        token: row.fire_base_id,
      };
      console.log("message", message);
      const msg = await admin.messaging().send(message);
      console.log("msg", msg);
      if (msg) {
        return msg;
      } else {
        return null;
      }
    } else {
      console.log("No firebase id");
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const encrypt = (text) => {
  let iv = crypto.randomBytes(IV_LENGTH);
  let cipher = crypto.createCipheriv(
    "aes-256-cbc",
    Buffer.from(ENCRYPTION_KEY),
    iv
  );
  let encrypted = cipher.update(text);

  encrypted = Buffer.concat([encrypted, cipher.final()]);

  return iv.toString("hex") + ":" + encrypted.toString("hex");
};

const decrypt = async (text) => {
  return new Promise((resolve, reject) => {
    try {
      let textParts = text.split(":");
      let iv = Buffer.from(textParts.shift(), "hex");
      let encryptedText = Buffer.from(textParts.join(":"), "hex");
      let decipher = crypto.createDecipheriv(
        "aes-256-cbc",
        Buffer.from(ENCRYPTION_KEY),
        iv
      );
      let decrypted = decipher.update(encryptedText);

      decrypted = Buffer.concat([decrypted, decipher.final()]);

      resolve(decrypted.toString());
    } catch (error) {
      reject(error);
    }
  });
};

const updateLastActiveService = async (payload) => {
  try {
    const procedure = `CALL spUpdateLastActive(?)`;
    const [[result]] = await db.promise().query(procedure, JSON.stringify(payload));
    return result;
  } catch (error) {
    console.log("error in updateLastActiveService", error);
    throw error;
  }
};

const markMessagesAsReadService = async (payload) => {
  try {
    console.log("Payload in markMessagesAsReadService::>>", payload);
    const procedure = `CALL spMarkMessagesAsRead(?)`;
    const [[row]] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row:>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in markMessagesAsReadService", error);
    throw error;
  }
};


module.exports = {
  msg91OtpService,
  msg91WhatsappService,
  trackerService,
  logOutService,
  getFirebaseNotificationService,
  deleteRequestService,
  parentByIdService,
  globalSearchService,
  getChatHistoryService,
  disableAccountService,
  reportParentsService,
  getCityListService,
  reportEventsService,
  reportClassService,
  sendOtpService,
  verifyOtpService,
  getUserDetailService,
  getRegisterUserDetailsService,
  getDetailsService,
  editUserDetailsService,
  getUserDetailsService,
  getConnectionListService,
  getConnectionByIdService,
  saveDeviceTokenService,
  getParentsListService,
  deleteUserService,
  ConnectionRequestService,
  BookmarkService,
  bookmarkListService,
  categoryListService,
  interestsCategoryService,
  reportEventsAndClassesService,
  parentAppealService,
  getNotificationListService,
  updateNotificationService,
  encrypt,
  decrypt,
  updateLastActiveService,
  markMessagesAsReadService,
};
