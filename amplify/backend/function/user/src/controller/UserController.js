const db = require("../config/db");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const { requiredParams } = require("../utility/requiredCheck");
const axios = require("axios");
const {
  getWhatsappService,
  getFirebaseNotificationService,
  trackerService,
  logOutService,
  deleteRequestService,
  parentByIdService,
  globalSearchService,
  getChatHistoryService,
  disableAccountService,
  reportParentsService,
  getCityListService,
  getMSG91Service,
  verifiedMSG91Service,
  reportEventsService,
  reportClassService,
  sendOtpService,
  verifyOtpService,
  getUserDetailService,
  editUserDetailsService,
  getRegisterUserDetailsService,
  getDetailsService,
  getConnectionListService,
  getUserDetailsService,
  getConnectionByIdService,
  saveDeviceTokenService,
  getParentsListService,
  ConnectionRequestService,
  deleteUserService,
  BookmarkService,
  bookmarkListService,
  categoryListService,
  interestsCategoryService,
  parentAppealService,
  reportEventsAndClassesService,
  getNotificationListService,
  updateNotificationService,
  encrypt,
  decrypt,
  updateLastActiveService,
} = require("../service/UserService");

const secretKey = "81cdaacb-888a-4e37-94ae-b7065ad7ddw3";
class UserController {
  static async sendOtp(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { mobile, name } = payload;
    const otp = Math.floor(1000 + Math.random() * 9000).toString();
    console.log("otp", otp);
    // const encryptedOtp = otp;
    const encryptedOtp = await bcrypt.hash(otp, 10);
    console.log("EncryptedOTP", encryptedOtp);
    const userPayload = { ...payload, otp: encryptedOtp, user_type: "Parent" };
    if (mobile == null || mobile == undefined)
      return requiredParams(res, "mobile is required!", 406);

    if (!/^\d{10}$/.test(mobile)) {
      return res.status(400).send({
        success: false,
        msg: "Mobile number must be 10 digits long",
        data: {},
      });
    }

    const isverified = `select isverified from OTP where mobile = ? AND user_type = 'Parent'`;
    const [[verifiedcheck]] = await db.promise().query(isverified, mobile);
    console.log("verified Check:>>", verifiedcheck);
    // if (verifiedcheck?.isverified === 1)
    //   return requiredParams(res, "User already verified", 406, verifiedcheck);
    let result;
    try {
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // await getWhatsappService({
      //   // user_id: isExist.temp_id,
      //   type: "temp",
      //   data: {
      //     message: `Your Otp is ${otp}.`,
      //     OTP: otp,
      //     mobile: mobile,
      //   },
      //   template_name: "parenthing_otp_testv3",
      //   body_values: {
      //     OTP: otp,
      //   },
      // });
      if (verifiedcheck && verifiedcheck.isverified === 1) {
        // await verifiedMSG91Service({
        //   mobile: mobile,
        //   otp: otp,
        // });
      } else if (
        verifiedcheck?.isverified == null ||
        verifiedcheck?.isverified == undefined ||
        verifiedcheck?.isverified == 0
      ) {
        console.log("else field");
        // await getMSG91Service({
        //   mobile: mobile,
        //   otp: otp,
        // });
      }

      // console.log("User already exist!", isExist.user_id);
      const updated = await sendOtpService(userPayload);
      console.log("updateOtp", updated);

      return res.status(200).send({
        success: true,
        msg: "Otp sent successfully.",
        data: {},
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async disableAccount(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { parent_id, type } = payload;

    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);

    const isverified = `select isactive from Users where parent_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, parent_id);
    console.log("verified Check:>>", verifiedcheck);

    let result;
    try {
      // console.log("User already exist!", isExist.user_id);
      const updated = await disableAccountService(userPayload);
      console.log("updateOtp", updated);

      return res.status(200).send({
        success: true,
        msg: "Account status updated successfully",
        data: {},
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async verifyOtp(req, res) {
    const payload = req.body;
    console.log("payload ", payload);
    const { otp, mobile } = payload;
    if (mobile == null || mobile == undefined)
      return requiredParams(res, "mobile is required!", 406);
    if (otp == null || otp == undefined)
      return requiredParams(res, "otp is required!", 406);

    //
    if (!/^\d{10}$/.test(mobile)) {
      return res.status(400).send({
        success: false,
        msg: "Mobile number must be 10 digits long",
        data: {},
      });
    }

    if (!/^\d{4}$/.test(otp)) {
      return res.status(400).send({
        success: false,
        msg: "OTP must be 4 digits only",
        data: {},
      });
    }

    const isactive = `select isactive from Users where mobile = ? and role_id = 1`;
    const [[activecheck]] = await db.promise().query(isactive, mobile);
    console.log("activecheck", activecheck);
    if (activecheck && activecheck.isactive === 0) {
      return res.status(403).send({
        success: false,
        msg: "Your account is disabled",
        data: {},
      });
    }

    const checkSQL = `select count(*) as status, otp_id as temp_id,sentotp as otp,parent_id,isverified,mobile,updated_at,DATE_ADD(updated_at,INTERVAL 1 MINUTE) as expire_time from OTP_parents where mobile = ? AND user_type = 'Parent'`;
    const [[isExist]] = await db.promise().query(checkSQL, mobile);
    console.log("isExist user", isExist);
    if (
      !payload.hasOwnProperty("role_id") ||
      payload.role_id === null ||
      payload.role_id === undefined
    ) {
      return requiredParams(res, "role_id is required!", 406);
    }
    // let result;
    try {
      if (new Date() > isExist.expire_time)
        return requiredParams(res, "otp expired!");
      const comparison = await bcrypt.compare(otp, isExist.otp);
      console.log("comparison", comparison);
      const parentData = {
        parent_id: isExist.parent_id,
      };
      if (isExist.isverified === 1 && (comparison || otp === "0000")) {
        const [[userData]] = await getDetailsService(parentData);
        console.log("Userdata::>>", userData);
        const proc = `CALL spIsProfileComplete(?)`;
        const [[[row]]] = await db
          .promise()
          .query(proc, JSON.stringify(parentData));
        console.log("spIsProfileComplete", row.status);
        const isComplete = row.status;
        const token = jwt.sign({ result: payload.parent_name }, secretKey, {
          expiresIn: "30d",
        });
        const userDatadetails = {
          ...userData,
          token: token,
          isprofileComplete: isComplete,
        };
        return res.status(200).send({
          success: true,
          msg: "Login Successfully",
          data: userDatadetails,
        });
      } else if (comparison == true || otp === "0000") {
        // const token = jwt.sign(
        //   { result: isExist.mobile },
        //   secretKey,
        //   {
        //     expiresIn: "30d",
        //   }
        // );
        const [user] = await verifyOtpService(payload);
        console.log("user", user);

        const [result] = await getUserDetailService({
          parent_id: user.parent_id,
          type: "newuser",
        });
        const data = { ...result, is_newuser: user.is_newuser };
        console.log("result", result);
        return res.status(200).send({
          success: true,
          msg: "User verified successfully.",
          data: data,
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "otp incorrect.",
          data: {},
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: true,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async logOut(req, res) {
    const payload = req.body;
    const { parent_id } = payload;
    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);

    const checkSQL = `SELECT count(*) as status from Users where parent_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [payload.parent_id]);

    try {
      if (isExist.status > 0) {
        const updated = await logOutService(payload);
        console.log("logOut", updated);

        return res.status(200).send({
          success: true,
          msg: "firebase_id deleted successfully.",
          data: {},
        });
      } else {
        return res.status(500).send({
          success: true,
          msg: "parent_id does not exists",
          data: {},
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async registerUser(req, res) {
    const payload = req.body;

    console.log("Payload::>>", payload);

    // if (
    //   !payload.hasOwnProperty("role_id") ||
    //   payload.role_id === null ||
    //   payload.role_id === undefined
    // ) {
    //   return requiredParams(res, "role_id is required!", 406);
    // }
    if (
      !payload.hasOwnProperty("parent_id") ||
      payload.parent_id === null ||
      payload.parent_id === undefined
    ) {
      return requiredParams(res, "parent_id is required!", 406);
    }
    // Check if name field is present and not empty
    // if (
    //   !payload.hasOwnProperty("parent_name") ||
    //   payload.parent_name.trim() === ""
    // ) {
    //   return requiredParams(res, "Name is required!", 406);
    // }

    //check if first name is present and not empty
    if (
      !payload.hasOwnProperty("first_name") ||
      payload.first_name.trim() === ""
    ) {
      return requiredParams(res, "first_name is required!", 406);
    }

    if (
      !payload.hasOwnProperty("last_name") ||
      payload.last_name.trim() === ""
    ) {
      return requiredParams(res, "last_name is required!", 406);
    }

    if (!payload.hasOwnProperty("dob") || payload.dob.trim() === "") {
      return requiredParams(res, "dob is required!", 406);
    }

    if (!payload.hasOwnProperty("gender") || payload.gender.trim() === "") {
      return requiredParams(res, "gender is required!", 406);
    }
    // Check if lat field is present and not empty
    // if (
    //   !payload.hasOwnProperty("lat") ||
    //   payload.lat === null ||
    //   payload.lat === undefined
    // ) {
    //   return requiredParams(res, "Latitude is required!", 406);
    // }

    // // Check if long field is present and not empty
    // if (
    //   !payload.hasOwnProperty("long") ||
    //   payload.long === null ||
    //   payload.long === undefined
    // ) {
    //   return requiredParams(res, "Longitude is required!", 406);
    // }

    // Check if parent_id field is present and not empty
    if (
      !payload.hasOwnProperty("parent_id") ||
      payload.parent_id === null ||
      payload.parent_id === undefined
    ) {
      return requiredParams(res, "Parent ID is required!", 406);
    }

    if (
      !payload.hasOwnProperty("kids_details") ||
      payload.kids_details === null ||
      payload.kids_details === undefined
    ) {
      return requiredParams(res, "Parent ID is required!", 406);
    }

    const checkSQL = `SELECT count(*) as status from Parents where parent_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [payload.parent_id]);
    console.log("isExist::>>", isExist);
    if (isExist.status > 0) {
      const data = await getRegisterUserDetailsService(payload);
      // console.log("Data:>>", data);

      const [[userData]] = await getDetailsService(payload);
      console.log("Userdata::>>", userData);
      const token = jwt.sign({ result: payload.parent_name }, secretKey, {
        expiresIn: "30d",
      });
      const userDatadetails = { ...userData, token: token };
      return res.status(200).send({
        success: true,
        msg: "User registration is successfull",
        data: userDatadetails,
      });
    } else {
      return res.status(500).send({
        success: true,
        msg: "User does not exists",
        data: {},
      });
    }
  }

  static async editUser(req, res) {
    const payload = req.body;

    console.log("Payload::>>", payload);

    // if (
    //   !payload.hasOwnProperty("role_id") ||
    //   payload.role_id === null ||
    //   payload.role_id === undefined
    // ) {
    //   return requiredParams(res, "role_id is required!", 406);
    // }
    if (
      !payload.hasOwnProperty("parent_id") ||
      payload.parent_id === null ||
      payload.parent_id === undefined
    ) {
      return requiredParams(res, "parent_id is required!", 406);
    }
    // Check if name field is present and not empty
    // if (
    //   !payload.hasOwnProperty("parent_name") ||
    //   payload.parent_name.trim() === ""
    // ) {
    //   return requiredParams(res, "Name is required!", 406);
    // }

    //check if first name is present and not empty

    // Check if lat field is present and not empty
    // if (
    //   !payload.hasOwnProperty("lat") ||
    //   payload.lat === null ||
    //   payload.lat === undefined
    // ) {
    //   return requiredParams(res, "Latitude is required!", 406);
    // }

    // // Check if long field is present and not empty
    // if (
    //   !payload.hasOwnProperty("long") ||
    //   payload.long === null ||
    //   payload.long === undefined
    // ) {
    //   return requiredParams(res, "Longitude is required!", 406);
    // }

    // Check if parent_id field is present and not empty

    const checkSQL = `SELECT count(*) as status from Parents where parent_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [payload.parent_id]);
    console.log("isExist::>>", isExist);
    if (isExist.status > 0) {
      const data = await editUserDetailsService(payload);
      console.log("Data:>>", data);

      // const [[userData]] = await getDetailsService(payload);

      return res.status(200).send({
        success: true,
        msg: "Profile edited successfully",
        data: {},
      });
    } else {
      return res.status(500).send({
        success: true,
        msg: "User does not exists",
        data: {},
      });
    }
  }

  static async getUserById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { parent_id } = payload;

    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);

    // if (verifiedcheck?.isverified === 1)
    //   return requiredParams(res, "User already verified", 406, verifiedcheck);

    const checkSQL = `SELECT count(*) as status from Parents where parent_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [payload.parent_id]);
    console.log("isExist::>>", isExist);
    if (isExist.status === 0) {
      return res.status(404).send({
        success: false,
        msg: "Requested Parent ID is not available",
        data: {},
      });
    }
    try {
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // console.log("User already exist!", isExist.user_id);
      const [[userData]] = await getUserDetailsService(payload);
      console.log("User data::>>", userData);
      const proc = `CALL spIsProfileComplete(?)`;
      const [[[row]]] = await db.promise().query(proc, JSON.stringify(payload));
      console.log("spIsProfileComplete", row.status);
      const isComplete = row.status;
      const userDatadetails = {
        ...userData,
        isprofileComplete: isComplete,
      };

      return res.status(200).send({
        success: true,
        msg: "user details fetched successfully",
        data: userDatadetails,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async parentById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { parent1_id, parent2_id } = payload;

    if (parent1_id == null || parent1_id == undefined)
      return requiredParams(res, "parent1_id is required!", 406);

    if (parent2_id == null || parent2_id == undefined)
      return requiredParams(res, "parent2_id is required!", 406);
    // if (verifiedcheck?.isverified === 1)
    //   return requiredParams(res, "User already verified", 406, verifiedcheck);

    // const checkSQL = `SELECT count(*) as status from Parents where parent_id = ?`;
    // const [[isExist]] = await db.promise().query(checkSQL, [payload.parent_id]);
    // console.log("isExist::>>", isExist);
    // if (isExist.status === 0) {
    //   return res.status(404).send({
    //     success: false,
    //     msg: "Requested Parent ID is not available",
    //     data: {},
    //   });
    // }
    try {
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // console.log("User already exist!", isExist.user_id);
      const [userData] = await parentByIdService(payload);
      console.log("User data::>>", userData);

      return res.status(200).send({
        success: true,
        msg: "parent data fetched successfully",
        data: userData,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async parentsAppeal(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { parent_id } = payload;

    if (parent_id === null || parent_id === undefined) {
      return res.status(400).json({
        success: false,
        msg: "parent_id is a required field",
        data: {},
      });
    }

    try {
      const [result] = await parentAppealService(payload);
      console.log("parent data::>>", result);

      return res.status(200).json({
        success: true,
        msg: "Parent appeal submitted successfully",
        data: {},
      });
    } catch (error) {
      console.error("Error message:", error);
      return res.status(500).json({
        success: false,
        msg: error.message || "Failed to submit parent appeal",
        data: {},
      });
    }
  }

  static async DeleteAccountById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { parent_id } = payload;

    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);

    // if (verifiedcheck?.isverified === 1)
    //   return requiredParams(res, "User already verified", 406, verifiedcheck);

    const checkSQL = `SELECT count(*) as status from Parents where parent_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [payload.parent_id]);
    console.log("isExist::>>", isExist);
    if (isExist.status === 0) {
      return res.status(404).send({
        success: false,
        msg: "Requested Parent ID is not available",
        data: {},
      });
    }
    try {
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // console.log("User already exist!", isExist.user_id);
      const [[userData]] = await deleteUserService(payload);
      console.log("User data::>>", userData);

      return res.status(200).send({
        success: true,
        msg: userData.message || "Account deleted successfully",
        data: {},
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async deleteRequest(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { parent_id } = payload;

    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);

    // if (verifiedcheck?.isverified === 1)
    //   return requiredParams(res, "User already verified", 406, verifiedcheck);

    const checkSQL = `SELECT count(*) as status from Parents where parent_id = ? AND is_deleted = 0`;
    const [[isExist]] = await db.promise().query(checkSQL, [payload.parent_id]);
    console.log("isExist::>>", isExist);
    if (isExist.status === 0) {
      return res.status(404).send({
        success: false,
        msg: "Requested Parent ID is already deleted",
        data: {},
      });
    }
    try {
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // console.log("User already exist!", isExist.user_id);
      const userData = await deleteRequestService(payload);
      console.log("User data::>>", userData);

      return res.status(200).send({
        success: true,
        msg: userData.message || "Account deleted successfully",
        data: {},
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getConnectionList(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { parent_id, type } = payload;

    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);

    if (type == null || type == undefined)
      return requiredParams(res, "type is required", 406);
    // if (verifiedcheck?.isverified === 1)
    //   return requiredParams(res, "User already verified", 406, verifiedcheck);

    const checkSQL = `SELECT count(*) as status from Connection_Master where sender_id = ? || receiver_id = ?`;
    const [[isExist]] = await db
      .promise()
      .query(checkSQL, [payload.parent_id, payload.parent_id]);
    console.log("isExist::>>", isExist);
    if (
      (isExist.status === 0 ||
        isExist.status == null ||
        isExist.status == undefined) &&
      type !== `received`
    ) {
      return res.status(404).send({
        success: false,
        msg: "Requested Parent ID has no connections",
        data: {},
      });
    }

    try {
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // console.log("User already exist!", isExist.user_id);
      const userData = await getConnectionListService(payload);
      console.log("User data::>>", userData);
      return res.status(200).send({
        success: true,
        msg: "Connection details fetched successfully",
        data: userData,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getConnectionById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { connection_id } = payload;

    if (connection_id == null || connection_id == undefined)
      return requiredParams(res, "connection_id is required!", 406);

    const checkSQL = `SELECT count(*) as status from Connection_Master where connection_id = ?`;
    const [[isExist]] = await db
      .promise()
      .query(checkSQL, [payload.connection_id]);
    console.log("isExist::>>", isExist);
    if (
      isExist.status === 0 ||
      isExist.status == null ||
      isExist.status == undefined
    ) {
      return res.status(404).send({
        success: false,
        msg: "Requested connection_id is not available",
        data: {},
      });
    }

    try {
      // console.log("User already exist!", isExist.user_id);
      const [[data]] = await getConnectionByIdService(payload);
      //   console.log("updateOtp", updated);
      console.log("data::>>", data);
      return res.status(200).send({
        success: true,
        msg: "Connection details",
        data: data,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async saveDeviceToken(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    // const { parent_id } = payload;

    const { parent_id, user_firebase_token } = payload;
    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);
    if (user_firebase_token == null || user_firebase_token == undefined)
      return requiredParams(res, "user_firebase_token is required!", 406);
    const checkSQL = `select count(*) as status from Users where parent_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, parent_id);
    try {
      if (isExist.status != 0) {
        const [result] = await saveDeviceTokenService(payload);
        if (result) {
          return res.status(200).send({
            success: true,
            msg: "Device Token Saved!",
            data: {},
          });
        }
      } else {
        return res.status(400).send({
          success: false,
          msg: "User does not exist!",
          data: {},
        });
      }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async getParentsList(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { parent_id } = payload;

    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);

    // if (verifiedcheck?.isverified === 1)
    //   return requiredParams(res, "User already verified", 406, verifiedcheck);

    const checkSQL = `SELECT count(*) as status from Parents where parent_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [payload.parent_id]);
    console.log("isExist::>>", isExist);
    if (
      isExist.status === 0 ||
      isExist.status == null ||
      isExist.status == undefined
    ) {
      return res.status(404).send({
        success: false,
        msg: "Requested Parent ID is not available",
        data: {},
      });
    }

    try {
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // console.log("User already exist!", isExist.user_id);
      const userData = await getParentsListService(payload);
      console.log("User data::>>", userData);
      return res.status(200).send({
        success: true,
        msg: "Parents List fetched successfully",
        data: userData,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async ConnectionRequest(req, res) {
    const payload = req.body;
    const { sender_id, receiver_id } = payload;
    console.log("payload", payload);
    // if (user_id == null || user_id == undefined) return requiredChecks(res, 'user_id is required!', 406);
    const checkSQL = `SELECT COUNT(*) AS status FROM Users WHERE parent_id IN (?, ?)`;
    const [[isExist]] = await db
      .promise()
      .query(checkSQL, [sender_id, receiver_id]);

    console.log("isExist user", isExist);
    if (isExist.status < 1) return requiredChecks(res, "User does not exist!");
    console.log("Check 1");
    try {
      const [result] = await ConnectionRequestService(payload);
      console.log("Check2");
      console.log("Result", result);
      if (result) {
        if (
          result.notification_id !== null &&
          result.notification_id !== undefined
        ) {
          const fb = await getFirebaseNotificationService({
            receiver_id: result.firebase_receiver_id,
            data: {
              title: result.title,
              body: result.msg,
              type: result.notification_type,
            },
          });
          console.log("Fb::>>", fb);
        }
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {},
        });
      }
    } catch (error) {
      console.log("inside error", error);
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async bookmark(req, res) {
    const payload = req.body;
    const { parent_id, bookmark_type, bookmarked_id, action } = payload;

    if (
      !payload.hasOwnProperty("parent_id") ||
      parent_id === null ||
      parent_id === undefined
    ) {
      return requiredParams(res, "parent_id is required!", 406);
    }
    if (
      !payload.hasOwnProperty("bookmark_type") ||
      bookmark_type === null ||
      bookmark_type === undefined
    ) {
      return requiredParams(res, "bookmark_type is required!", 406);
    }
    if (
      !payload.hasOwnProperty("bookmarked_id") ||
      bookmarked_id === null ||
      bookmarked_id === undefined
    ) {
      return requiredParams(res, "bookmarked_id is required!", 406);
    }
    if (
      !payload.hasOwnProperty("action") ||
      action === null ||
      action === undefined
    ) {
      return requiredParams(res, "action is required!", 406);
    }

    // if (user_id == null || user_id == undefined) return requiredChecks(res, 'user_id is required!', 406);
    const checkSQL = `SELECT COUNT(*) AS status FROM Users WHERE parent_id IN (?)`;
    const [[isExist]] = await db.promise().query(checkSQL, [parent_id]);
    // const bookmarkCheck = `SELECT COUNT(*) AS status FROM eventbookmarks WHERE parent_id = ? AND  `
    // const [[bmexist]] = await db.promise().query(checkSQL, [parent_id]);
    // if(action==="add" && bmexist.status >0){
    //   return requiredParams(res, "Bookmark already added", 406);
    // }

    console.log("isExist user", isExist);
    if (isExist.status < 1) return requiredChecks(res, "User does not exist!");
    try {
      const [result] = await BookmarkService(payload);
      console.log("Result", result);
      if (result) {
        // if (result.notification_id !== null || result.notification_id !== undefined) {
        //   await getFirebaseNotificationService({
        //     "receiver_id": result.send_to.user_id,
        //     "data": {
        //       "title": result.send_to.title,
        //       "body": result.send_to.msg,
        //       "type": result.send_to.type
        //     }
        //   });
        // }
        return res.status(200).send({
          success: true,
          msg: `${result.msg}`,
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async bookmarklist(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { parent_id, bookmark_type } = payload;

    if (
      !payload.hasOwnProperty("parent_id") ||
      parent_id === null ||
      parent_id === undefined
    ) {
      return requiredParams(res, "parent_id is required!", 406);
    }
    if (
      !payload.hasOwnProperty("bookmark_type") ||
      bookmark_type === null ||
      bookmark_type === undefined
    ) {
      return requiredParams(res, "bookmark_type is required!", 406);
    }
    const checkSQL = `SELECT count(*) as status from Parents where parent_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [payload.parent_id]);
    console.log("isExist::>>", isExist);
    if (
      isExist.status === 0 ||
      isExist.status == null ||
      isExist.status == undefined
    ) {
      return res.status(404).send({
        success: false,
        msg: "Requested Parent ID is not available",
        data: {},
      });
    }

    try {
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // console.log("User already exist!", isExist.user_id);
      const userData = await bookmarkListService(payload);
      console.log("User data::>>", userData);
      // userData.forEach((item) => {
      //   item.bookmark_flag = true;
      // });
      return res.status(200).send({
        success: true,
        msg: "List fetched successfully",
        data: userData,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async globalSearch(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { search, parent_id, type, latitude, longitude, city } = payload;

    if (
      !payload.hasOwnProperty("search") ||
      search === null ||
      search === undefined
    ) {
      return requiredParams(res, "search is required!", 406);
    }
    if (
      !payload.hasOwnProperty("parent_id") ||
      parent_id === null ||
      parent_id === undefined
    ) {
      return requiredParams(res, "parent_id is required!", 406);
    }
    if (
      !payload.hasOwnProperty("type") ||
      type === null ||
      type === undefined
    ) {
      return requiredParams(res, "type is required!", 406);
    }
    if (
      !payload.hasOwnProperty("latitude") ||
      latitude === null ||
      latitude === undefined
    ) {
      return requiredParams(res, "latitude is required!", 406);
    }
    if (
      !payload.hasOwnProperty("longitude") ||
      longitude === null ||
      longitude === undefined
    ) {
      return requiredParams(res, "longitude is required!", 406);
    }

    if (
      !payload.hasOwnProperty("city") ||
      city === null ||
      city === undefined
    ) {
      return requiredParams(res, "city is required!", 406);
    }

    const checkSQL = `SELECT count(*) as status from Serviceable where city = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [payload.city]);
    console.log("isExist::>>", isExist);
    if (
      isExist.status === 0 ||
      isExist.status == null ||
      isExist.status == undefined
    ) {
      return res.status(404).send({
        success: false,
        msg: "Not in serviceable city list",
        data: {},
      });
    }

    try {
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // console.log("User already exist!", isExist.user_id);
      const userData = await globalSearchService(payload);
      console.log("User data::>>", userData);
      // userData.forEach((item) => {
      //   item.bookmark_flag = true;
      // });
      return res.status(200).send({
        success: true,
        msg: "List fetched successfully",
        data: userData,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async categoryList(req, res) {
    try {
      const [result] = await categoryListService();
      console.log("Result:", result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Category List",
          data: result,
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "No categories found.",
          data: [],
        });
      }
    } catch (error) {
      console.log("Error in categoryList:", error);
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }

  static async interestsCategory(req, res) {
    try {
      const [result] = await interestsCategoryService();
      console.log("result::>>");
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Interest List",
          data: result,
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "Check your input.",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }

  static async reportEventsAndClasses(req, res) {
    const payload = req.body;
    console.log("Req.body", req.body);
    const { parent_id, reason, event_id, class_id } = payload;
    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);
    if (reason == null || reason == undefined)
      return requiredParams(res, "reason is required!", 406);

    if (event_id == null && class_id == null) {
      return requiredParams(
        res,
        "Either event_id or class_id must be present!",
        406
      );
    }

    // console.log("Parent_id", parent_id);
    // const checkevent = `SELECT COUNT(*) AS status FROM reportedevents where parent_id = ? `;
    // // const checkclass = `SELECT COUNT(*) AS status FROM reportedclasses where parent_id = ? AND class_id = ?`;
    // const eventReportExist = await db.promise(checkevent, parent_id);

    // const [[classReportExist]] = await db.promise(checkclass, [
    //   parent_id,
    //   class_id,
    // ]);

    // console.log("eventReportExist", eventReportExist);
    // console.log("classReportExist", classReportExist);
    // if (eventReportExist.status > 0) {
    //   return requiredParams(res, "event_id already reported", 406);
    // }

    // if (classReportExist.status > 0) {
    //   return requiredParams(res, "class_id already reported", 406);
    // }

    const checkSQL = `SELECT COUNT(*) AS status FROM Users WHERE parent_id IN (?)`;
    const [[isExist]] = await db.promise().query(checkSQL, [parent_id]);

    console.log("isExist user", isExist);
    if (isExist.status < 1) return requiredParams(res, "User does not exist!");
    try {
      const result = await reportEventsAndClassesService(payload);
      console.log("Result", result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Reported successfully",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async reportEvent(req, res) {
    const payload = req.body;
    console.log("Req.body", req.body);
    const { parent_id, reason, event_id } = payload;
    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);
    if (reason == null || reason == undefined)
      return requiredParams(res, "reason is required!", 406);

    if (event_id == null || event_id == undefined) {
      return requiredParams(res, "event_id is required", 406);
    }
    // const checkevent = `SELECT COUNT(*) AS status FROM reportedevents WHERE parent_id = ?`;

    // try {
    //   const [rows, fields] = await db.promise().query(checkevent, [parent_id]);
    //   const eventReportExist = rows[0].status;
    //   console.log("Event report exist: ", eventReportExist);
    // } catch (error) {
    //   console.error("Error querying the database: ", error);
    // }

    const checkevent = `SELECT COUNT(*) AS status FROM reportedevents where parent_id = ? AND event_id = ? `;
    const [[eventReportExist]] = await db
      .promise()
      .query(checkevent, [parent_id, event_id]);
    console.log("Event report exist::>>", eventReportExist);

    // console.log("Parent_id", parent_id);
    // const checkevent = `SELECT COUNT(*) AS status FROM reportedevents where parent_id = ? `;
    // // const checkclass = `SELECT COUNT(*) AS status FROM reportedclasses where parent_id = ? AND class_id = ?`;
    // const eventReportExist = await db.promise(checkevent, parent_id);

    // const [[classReportExist]] = await db.promise(checkclass, [
    //   parent_id,
    //   class_id,
    // ]);

    // console.log("eventReportExist", eventReportExist);
    // console.log("classReportExist", classReportExist);
    if (eventReportExist.status > 0) {
      return requiredParams(res, "event_id already reported", 406);
    }

    const eventExists = `SELECT COUNT(*) AS status FROM events where event_id = ? `;
    const [[eventExiststatus]] = await db
      .promise()
      .query(eventExists, [event_id]);
    console.log("Event report exist::>>", eventReportExist);

    if (eventExiststatus.status === 0) {
      return requiredParams(res, "event_id doesn't exists", 406);
    }
    // if (classReportExist.status > 0) {
    //   return requiredParams(res, "class_id already reported", 406);
    // }

    // const checkSQL = `SELECT COUNT(*) AS status FROM Users WHERE parent_id IN (?)`;
    // const [[isExist]] = await db.promise().query(checkSQL, [parent_id]);

    // console.log("isExist user", isExist);
    // if (isExist.status < 1) return requiredParams(res, "User does not exist!");
    try {
      const result = await reportEventsService(payload);
      console.log("Result", result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Reported successfully",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async reportClass(req, res) {
    const payload = req.body;
    console.log("Req.body", req.body);
    const { parent_id, reason, class_id } = payload;
    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);
    if (reason == null || reason == undefined)
      return requiredParams(res, "reason is required!", 406);

    if (class_id == null || class_id == undefined) {
      return requiredParams(res, "class_id is required", 406);
    }
    // const checkevent = `SELECT COUNT(*) AS status FROM reportedevents WHERE parent_id = ?`;

    // try {
    //   const [rows, fields] = await db.promise().query(checkevent, [parent_id]);
    //   const eventReportExist = rows[0].status;
    //   console.log("Event report exist: ", eventReportExist);
    // } catch (error) {
    //   console.error("Error querying the database: ", error);
    // }

    const checkevent = `SELECT COUNT(*) AS status FROM reportedclasses where parent_id = ? AND class_id = ? `;
    const [[classReportExist]] = await db
      .promise()
      .query(checkevent, [parent_id, class_id]);
    console.log("class report exist::>>", classReportExist);

    // console.log("Parent_id", parent_id);
    // const checkevent = `SELECT COUNT(*) AS status FROM reportedevents where parent_id = ? `;
    // // const checkclass = `SELECT COUNT(*) AS status FROM reportedclasses where parent_id = ? AND class_id = ?`;
    // const eventReportExist = await db.promise(checkevent, parent_id);

    // const [[classReportExist]] = await db.promise(checkclass, [
    //   parent_id,
    //   class_id,
    // ]);

    // console.log("eventReportExist", eventReportExist);
    // console.log("classReportExist", classReportExist);
    if (classReportExist.status > 0) {
      return requiredParams(res, "class_id already reported", 406);
    }

    const classExists = `SELECT COUNT(*) AS status FROM classes where class_id = ? `;
    const [[classExiststatus]] = await db
      .promise()
      .query(classExists, [class_id]);
    console.log("class report exist::>>", classExiststatus);

    if (classExiststatus.status === 0) {
      return requiredParams(res, "class_id doesn't exists", 406);
    }
    // if (classReportExist.status > 0) {
    //   return requiredParams(res, "class_id already reported", 406);
    // }

    // const checkSQL = `SELECT COUNT(*) AS status FROM Users WHERE parent_id IN (?)`;
    // const [[isExist]] = await db.promise().query(checkSQL, [parent_id]);

    // console.log("isExist user", isExist);
    // if (isExist.status < 1) return requiredParams(res, "User does not exist!");
    try {
      const result = await reportClassService(payload);
      console.log("Result", result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Reported successfully",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getCityList(req, res) {
    try {
      const [result] = await getCityListService();
      console.log("Result:", result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Serviceable City List",
          data: result,
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "No cities found.",
          data: [],
        });
      }
    } catch (error) {
      console.log("Error in getCityList:", error);
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }

  // static async reportParent(req, res) {
  //   const payload = req.body;
  //   console.log("Req.body", req.body);
  //   const { parent_id, reason, report_parent_id } = payload;
  //   if (parent_id == null || parent_id == undefined)
  //     return requiredParams(res, "parent_id is required!", 406);
  //   if (reason == null || reason == undefined)
  //     return requiredParams(res, "reason is required!", 406);

  //   if (report_parent_id == null || report_parent_id == undefined) {
  //     return requiredParams(res, "report_parent_id is required", 406);
  //   }
  //   // const checkevent = `SELECT COUNT(*) AS status FROM reportedevents WHERE parent_id = ?`;

  //   // try {
  //   //   const [rows, fields] = await db.promise().query(checkevent, [parent_id]);
  //   //   const eventReportExist = rows[0].status;
  //   //   console.log("Event report exist: ", eventReportExist);
  //   // } catch (error) {
  //   //   console.error("Error querying the database: ", error);
  //   // }

  //   const checkevent = `SELECT COUNT(*) AS status FROM reportedparents where parent_id = ? AND reported_parent_id = ? `;
  //   const [[eventReportExist]] = await db
  //     .promise()
  //     .query(checkevent, [parent_id, report_parent_id]);
  //   console.log("Event report exist::>>", eventReportExist);

  //   // console.log("Parent_id", parent_id);
  //   // const checkevent = `SELECT COUNT(*) AS status FROM reportedevents where parent_id = ? `;
  //   // // const checkclass = `SELECT COUNT(*) AS status FROM reportedclasses where parent_id = ? AND class_id = ?`;
  //   // const eventReportExist = await db.promise(checkevent, parent_id);

  //   // const [[classReportExist]] = await db.promise(checkclass, [
  //   //   parent_id,
  //   //   class_id,
  //   // ]);

  //   // console.log("eventReportExist", eventReportExist);
  //   // console.log("classReportExist", classReportExist);
  //   if (eventReportExist.status > 0) {
  //     return requiredParams(res, "parent_id already reported", 406);
  //   }

  //   const eventExists = `SELECT COUNT(*) AS status FROM Parents where parent_id = ? `;
  //   const [[eventExiststatus]] = await db
  //     .promise()
  //     .query(eventExists, [parent_id]);
  //   console.log("Event report exist::>>", eventReportExist);

  //   if (eventExiststatus.status === 0) {
  //     return requiredParams(res, "parent_id doesn't exists", 406);
  //   }
  //   // if (classReportExist.status > 0) {
  //   //   return requiredParams(res, "class_id already reported", 406);
  //   // }

  //   // const checkSQL = `SELECT COUNT(*) AS status FROM Users WHERE parent_id IN (?)`;
  //   // const [[isExist]] = await db.promise().query(checkSQL, [parent_id]);

  //   // console.log("isExist user", isExist);
  //   // if (isExist.status < 1) return requiredParams(res, "User does not exist!");
  //   try {
  //     const result = await reportParentsService(payload);
  //     console.log("Result", result);
  //     if (result) {
  //       return res.status(200).send({
  //         success: true,
  //         msg: "Reported successfully",
  //         data: {},
  //       });
  //     }
  //   } catch (error) {
  //     return res.status(500).send({
  //       success: false,
  //       msg: error.message || `Something went wrong`,
  //       data: {},
  //     });
  //   }
  // }

  static async reportParent(req, res) {
    const payload = req.body;
    console.log("Req.body", req.body);
    const { parent_id, reason, report_parent_id } = payload;
    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);
    if (reason == null || reason == undefined)
      return requiredParams(res, "reason is required!", 406);

    if (report_parent_id == null || report_parent_id == undefined) {
      return requiredParams(res, "report_parent_id is required", 406);
    }

    const checkevent = `SELECT COUNT(*) AS status FROM reportedparents where parent_id = ? AND reported_parent_id = ? `;
    const [[eventReportExist]] = await db
      .promise()
      .query(checkevent, [parent_id, report_parent_id]);
    console.log("Event report exist::>>", eventReportExist);

    if (eventReportExist.status > 0) {
      return requiredParams(res, "parent_id already reported", 406);
    }

    const eventExists = `SELECT COUNT(*) AS status FROM Parents where parent_id = ? `;
    const [[eventExiststatus]] = await db
      .promise()
      .query(eventExists, [parent_id]);
    console.log("Event report exist::>>", eventReportExist);

    if (eventExiststatus.status === 0) {
      return requiredParams(res, "parent_id doesn't exists", 406);
    }

    try {
      const result = await reportParentsService(payload);

      if (result) {
        const [[reportedUser]] = await db
          .promise()
          .query(
            `SELECT user_id,parent_id,first_name,last_name  FROM Parents where parent_id = ? `,
            [report_parent_id]
          );
        const [[reportedby]] = await db
          .promise()
          .query(
            `SELECT user_id,parent_id,first_name,last_name  FROM Parents where parent_id = ? `,
            [parent_id]
          );

        console.log(reportedUser, reportedby);
        const user_name = `${reportedby.first_name} ${reportedby.last_name}`;
        const receiver_id = reportedUser.parent_id;
        const receiver_user_id = reportedUser.user_id;
        console.log("user   id reported ", receiver_user_id);
        const data = {
          title: "Profile reported",
          body: `Your profile has been reported by ${user_name} for ${reason}`,
          notification_type: "user",
        };
        const notificationPayload = { receiver_id, data };

        try {
          const notification = await getFirebaseNotificationService(
            notificationPayload
          );
          console.log("profile report notification is ", notification);
          if (notification) {
            console.log(
              `profile report notification sent to user successfully`
            );

            // update notification table
            try {
              const [result] = await db
                .promise()
                .query("CALL spInsertNotification(?, ?, ?, ?, ?, ?)", [
                  receiver_user_id,
                  null,
                  1,
                  data.title,
                  data.body,
                  data.notification_type,
                ]);
              return res.status(200).send({
                success: true,
                msg: "Reported successfully",
                data: {},
              });
            } catch (error) {
              console.error("Error inserting notification:", error);
              throw error;
            }
          }
        } catch (error) {
          console.error("Error sending notification:", error);
          return res.status(500).send({
            success: false,
            msg: "Error sending notification",
            data: {},
          });
        }
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getChatHistory(req, res) {
    const payload = req.body;
    const result = await getChatHistoryService(payload);
    console.log("result", result);
    // if (result == undefined) return requiredParams(res, 'Error in service!', 406, []);
    // if (result.data.length == 0) return requiredParams(res, 'No Chats!', 200, [], true);
    try {
      // if (result) {
      return res.status(200).send({
        success: true,
        msg: `Chat History List`,
        data: result.data || [],
        meta: result.meta[0] || { total: 0 },
      });
      // }
    } catch (error) {
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: [],
      });
    }
  }

  static async tracker(req, res) {
    const { parent_id, type, event_id, class_id } = req.body;

    // Helper function to check if a value is null, empty, or undefined
    const isInvalid = (value) =>
      value === null || value === undefined || value === "";

    // Check if essential keys have valid data
    if (isInvalid(parent_id)) {
      return requiredParams(res, "parent_id is required!", 406);
    }

    if (isInvalid(type)) {
      return requiredParams(res, "type is required!", 406);
    }

    if (type === "event" && isInvalid(event_id)) {
      return requiredParams(res, "event_id is required for event type!", 406);
    }

    if (type === "class" && isInvalid(class_id)) {
      return requiredParams(res, "class_id is required for class type!", 406);
    }

    try {
      // Check if the parent exists
      const checkParentQuery = `SELECT COUNT(*) AS status FROM Parents WHERE parent_id = ?`;
      const [[parentCheck]] = await db
        .promise()
        .query(checkParentQuery, [parent_id]);

      if (parentCheck.status < 1) {
        return requiredParams(res, "parent_id doesn't exist", 404);
      }

      // Conditional check based on type
      let checkInterestQuery;
      let checkInterestParams;

      if (type === "event") {
        checkInterestQuery = `SELECT COUNT(*) AS status FROM event_interest_tracking WHERE parent_id = ? AND event_id = ?`;
        checkInterestParams = [parent_id, event_id];
      } else if (type === "class") {
        checkInterestQuery = `SELECT COUNT(*) AS status FROM class_interest_tracking WHERE parent_id = ? AND class_id = ?`;
        checkInterestParams = [parent_id, class_id];
      }

      if (checkInterestQuery && checkInterestParams) {
        const [[interestCheck]] = await db
          .promise()
          .query(checkInterestQuery, checkInterestParams);

        if (interestCheck.status === 1) {
          return requiredParams(res, "user already used this cta", 409);
        }
      }

      // Call the tracker service
      const result = await trackerService(req.body);

      if (result) {
        return res.status(200).send({
          success: true,
          msg: "cta enabled successfully",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }

  static async getFirebaseNotification(req, res) {
    try {
      const response = await getFirebaseNotificationService(req.body);
      console.log("Message_id", response);
      if (response) {
        return res.status(200).send({
          success: true,
          msg: `Notification Sent`,
          data: response,
        });
      } else {
        return res.status(400).send({
          success: false,
          msg: `Notification Error`,
          data: {},
        });
      }
    } catch (error) {
      console.log("Error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  // static async getNotificationList(req, res) {
  //   const payload = req.body;
  //   const { parent_id } = payload;
  //   if (parent_id == null || parent_id == undefined)
  //     return requiredParams(res, "parent_id is required!", 406);

  //   try {
  //     const [result] = await getNotificationListService(payload);
  //     console.log("Result:", result);
  //     if (result) {
  //       return res.status(200).send({
  //         success: true,
  //         msg: "Notifications List",
  //         data: result,
  //       });
  //     } else {
  //       return res.status(200).send({
  //         success: false,
  //         msg: "No Notification found.",
  //         data: [],
  //       });
  //     }
  //   } catch (error) {
  //     console.log("Error in getNotificationList:", error);
  //     return res.status(500).send({
  //       success: false,
  //       msg: error.message || "Something went wrong",
  //       data: {},
  //     });
  //   }
  // }

 static async getNotificationList(req, res) {
  const payload = req.body;
  const { parent_id } = payload;

  if (parent_id === null || parent_id === undefined) {
    return requiredParams(res, "parent_id is required!", 406);
  }

  try {
    const [result] = await getNotificationListService(payload);
    console.log("Raw notification result:", result);

    // Handle no notifications case
    if (!result || !result[0]) {
      return res.status(200).send({
        success: true,
        msg: "No notifications found",
        data: {
          is_new: 0,
          notifications: []
        }
      });
    }

    // Format the response with safe JSON parsing
    let formattedResult;
    try {
      formattedResult = {
        is_new: result[0].is_new || 0,
        notifications: result[0].notifications ? 
          (typeof result[0].notifications === 'string' ? 
            JSON.parse(result[0].notifications) : result[0].notifications) : []
      };
    } catch (parseError) {
      console.error("Error parsing notifications JSON:", parseError);
      return res.status(500).send({
        success: false,
        msg: "Error processing notifications data",
        data: {
          is_new: 0,
          notifications: []
        }
      });
    }

    return res.status(200).send({
      success: true,
      msg: "Notifications List",
      data: formattedResult
    });

  } catch (error) {
    console.error("Error in getNotificationList:", error);
    return res.status(500).send({
      success: false,
      msg: error.message || "Something went wrong",
      data: {
        is_new: 0,
        notifications: []
      }
    });
  }
}

  static async updateNotification(req, res) {
    const payload = req.body;
    const { parent_id } = payload;

    if (parent_id === null || parent_id === undefined) {
      return requiredParams(res, "parent_id is required!", 406);
    }

    try {
      const result = await updateNotificationService(payload);
      
      if (result) {
        return res.status(200).send({
          success: true,
          msg: result.message || "Notifications updated successfully",
          data: {}
        });
      }

      return res.status(400).send({
        success: false,
        msg: "Failed to update notifications",
        data: {}
      });

    } catch (error) {
      console.error("Error in updateNotification:", error);
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {}
      });
    }
  }

  static async sendMsg(req, res) {
    const { message, parent_id, connection_id } = req.body;

    if (!message || !parent_id || !connection_id) {
      return res
        .status(400)
        .send({ success: false, msg: "Missing required fields" });
    }

    const encryptedMessage = encrypt(message);

    try {
      const query = `INSERT INTO chat_master (message, parent_id, connection_id, message_type) VALUES (?, ?, ?, ?)`;
      await db
        .promise()
        .query(query, [encryptedMessage, parent_id, connection_id, "text"]);

      res.status(200).send({ success: true, msg: "Message sent successfully" });
    } catch (error) {
      console.error("Error storing message:", error);
      res.status(500).send({ success: false, msg: "Error storing message" });
    }
  }

  static async getMsg(req, res) {
    const { connection_id } = req.query;

    if (!connection_id) {
      return res
        .status(400)
        .send({ success: false, msg: "Missing required fields" });
    }

    try {
      const query = `SELECT message, parent_id, connection_id, message_type, created_at FROM chat_master WHERE connection_id = ? ORDER BY id DESC`;
      const [rows] = await db.promise().query(query, [connection_id]);

      const decryptedMessages = await Promise.all(
        rows.map(async (row) => {
          const decryptedMessage = await decrypt(row.message);
          return {
            ...row,
            message: decryptedMessage,
          };
        })
      );
      console.log("decrypted message is ", decryptedMessages);
      res.status(200).send({ success: true, messages: decryptedMessages });
    } catch (error) {
      console.error("Error retrieving messages:", error);
      res
        .status(500)
        .send({ success: false, msg: "Error retrieving messages" });
    }
  }

  static async updateLastActive(req, res) {
    const payload = req.body;
    
    // Validate that at least one ID is provided
    if (!payload.parent_id && !payload.business_id) {
      return requiredParams(res, "Either parent_id or business_id is required!", 406);
    }

    try {
      const result = await updateLastActiveService(payload);
      console.log("result -" ,result);

      return res.status(200).send({
      success: true,
      msg: "Last active status updated successfully"
      });

    } catch (error) {
      console.error("Error in updateLastActive:", error);
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
      });
    }
  }

}

module.exports = UserController;
