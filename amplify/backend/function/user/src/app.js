/*
Copyright 2017 - 2017 Amazon.com, Inc. or its affiliates. All Rights Reserved.
Licensed under the Apache License, Version 2.0 (the "License"). You may not use this file except in compliance with the License. A copy of the License is located at
    http://aws.amazon.com/apache2.0/
or in the "license" file accompanying this file. This file is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and limitations under the License.
*/

const express = require("express");
const bodyParser = require("body-parser");
const awsServerlessExpressMiddleware = require("aws-serverless-express/middleware");
const UserController = require("./controller/UserController");
const Authorization = require("./auth/tokenValidator");

// declare a new express app
const app = express();
app.use(bodyParser.json());
app.use(awsServerlessExpressMiddleware.eventContext());

// Enable CORS for all methods
app.use(function (req, res, next) {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Headers", "*");
  next();
});

/**********************
 * Example get method *
 **********************/

app.get("/mobile/user/getCategory", Authorization, UserController.categoryList);
app.get(
  "/mobile/user/interestsCategory",
  Authorization,
  UserController.interestsCategory
);
app.get("/mobile/user/getCityList", Authorization, UserController.getCityList);

app.get("/mobile/user", function (req, res) {
  // Add your code here
  res.json({ success: "get call succeed!", url: req.url });
});

app.get("/mobile/user/*", function (req, res) {
  // Add your code here
  res.json({ success: "get call succeed!", url: req.url });
});

/****************************
 * Example post method *
 ****************************/

app.post("/mobile/user/generateotp", UserController.sendOtp);
app.post("/mobile/user/verifyotp", UserController.verifyOtp);
app.post("/mobile/user/register", UserController.registerUser);
app.post("/mobile/user/editUser", Authorization, UserController.editUser);
app.post("/mobile/user/getUserById", Authorization, UserController.getUserById);
app.post(
  "/mobile/user/DeleteAccountById",
  Authorization,
  UserController.DeleteAccountById
);
app.post(
  "/mobile/user/connectionList",
  Authorization,
  UserController.getConnectionList
);
app.post(
  "/mobile/user/getConnectionById",
  Authorization,
  UserController.getConnectionById
);
app.post(
  "/mobile/user/saveDeviceToken",
  Authorization,
  UserController.saveDeviceToken
);
app.post(
  "/mobile/user/getParentsList",
  Authorization,
  UserController.getParentsList
);
app.post(
  "/mobile/user/reportParent",
  Authorization,
  UserController.reportParent
);
app.post(
  "/mobile/user/connectionRequest",
  Authorization,
  UserController.ConnectionRequest
);
app.post("/mobile/user/bookmark", Authorization, UserController.bookmark);
app.post(
  "/mobile/user/bookmarklist",
  Authorization,
  UserController.bookmarklist
);
app.post(
  "/mobile/user/reportEventsAndClasses",
  Authorization,
  UserController.reportEventsAndClasses
);
app.post("/mobile/user/tracker", Authorization, UserController.tracker);
app.post("/mobile/user/logOut", Authorization, UserController.logOut);
app.post("/mobile/user/reportEvent", Authorization, UserController.reportEvent);
app.post("/mobile/user/reportClass", Authorization, UserController.reportClass);
app.post(
  "/mobile/user/disableAccount",
  Authorization,
  UserController.disableAccount
);
app.post(
  "/mobile/user/deleteRequest",
  Authorization,
  UserController.deleteRequest
);
app.post(
  "/mobile/user/getChatHistory",
  Authorization,
  UserController.getChatHistory
);

app.post("/mobile/user/sendMessage", UserController.sendMsg);
app.post(
  "/mobile/user/globalSearch",
  Authorization,
  UserController.globalSearch
);
app.post("/mobile/user/parentById", Authorization, UserController.parentById);

app.post(
  "/mobile/user/getFirebaseNotification",
  UserController.getFirebaseNotification
);

app.post(
  "/mobile/user/getNotificationList",
  UserController.getNotificationList
);

app.post(
  "/mobile/user/updateNotification",
  UserController.updateNotification
);

app.post(
  "/mobile/user/updateLastActive",
  Authorization,
  UserController.updateLastActive
);

app.post("/mobile/user", function (req, res) {
  // Add your code here
  res.json({ success: "post call succeed!", url: req.url, body: req.body });
});

app.post("/mobile/user/parentsAppeal", UserController.parentsAppeal);

app.post("/mobile/user/markMessagesAsRead", UserController.markMessagesAsRead);

app.post("/mobile/user/*", function (req, res) {
  // Add your code here
  res.json({ success: "post call succeed!", url: req.url, body: req.body });
});



/****************************
 * Example put method *
 ****************************/

app.put("/mobile/user", function (req, res) {
  // Add your code here
  res.json({ success: "put call succeed!", url: req.url, body: req.body });
});

app.put("/mobile/user/*", function (req, res) {
  // Add your code here
  res.json({ success: "put call succeed!", url: req.url, body: req.body });
});

/****************************
 * Example delete method *
 ****************************/

app.delete("/mobile/user", function (req, res) {
  // Add your code here
  res.json({ success: "delete call succeed!", url: req.url });
});

app.delete("/mobile/user/*", function (req, res) {
  // Add your code here
  res.json({ success: "delete call succeed!", url: req.url });
});

app.listen(3000, function () {
  console.log("App started");
});

// Export the app object. When executing the application local this does nothing. However,
// to port it to AWS Lambda we will create a wrapper around that will load the app from
// this file
module.exports = app;
